#!/usr/bin/env python3
"""
Final integration test for SmallDoge WebUI
"""

import sys
import os
import requests
import json
import asyncio
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Backend API configuration
BACKEND_URL = "http://localhost:8000"
API_BASE = f"{BACKEND_URL}/api/v1"

def get_auth_token():
    """Get authentication token"""
    try:
        # Try to sign in with existing user
        signin_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }

        response = requests.post(f"{API_BASE}/auth/signin", json=signin_data)
        if response.status_code == 200:
            data = response.json()
            return data.get("token")

        # If signin fails, try to create the user
        print(f"Signin failed: {response.status_code}, trying to create user...")

        signup_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }

        response = requests.post(f"{API_BASE}/auth/signup", json=signup_data)
        if response.status_code == 200:
            data = response.json()
            return data.get("token")

        print(f"Signup also failed: {response.status_code} - {response.text}")
        return None

    except Exception as e:
        print(f"Error getting auth token: {e}")
        return None

def test_model_list():
    """Test model listing"""
    print("Testing model list...")
    
    token = get_auth_token()
    if not token:
        print("✗ Could not get authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{API_BASE}/models", headers=headers)
        print(f"Model list response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            models = data.get("models", [])
            print(f"✓ Found {len(models)} models:")
            for model in models:
                print(f"  - {model.get('id')}: {model.get('name')}")
            return True
        else:
            print(f"✗ Model list failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Model list error: {e}")
        return False

def test_doge_chat():
    """Test chat with Doge-160M model"""
    print("\nTesting chat with Doge-160M...")
    
    token = get_auth_token()
    if not token:
        print("✗ Could not get authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test non-streaming first
    print("1. Testing non-streaming chat...")
    chat_request = {
        "model": "Doge-160M",
        "messages": [
            {"role": "user", "content": "Hello! Please introduce yourself."}
        ],
        "stream": False,
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/inference/chat/completions",
            json=chat_request,
            headers=headers
        )
        print(f"Chat response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if 'choices' in data and data['choices']:
                content = data['choices'][0]['message']['content']
                print(f"✓ Chat response: {content[:100]}...")
                return True
            else:
                print(f"✗ Invalid response format: {data}")
                return False
        else:
            print(f"✗ Chat failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Chat error: {e}")
        return False

def test_streaming_chat():
    """Test streaming chat"""
    print("\n2. Testing streaming chat...")
    
    token = get_auth_token()
    if not token:
        print("✗ Could not get authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    chat_request = {
        "model": "Doge-160M",
        "messages": [
            {"role": "user", "content": "Tell me a short joke."}
        ],
        "stream": True,
        "temperature": 0.7,
        "max_tokens": 50
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/inference/chat/completions",
            json=chat_request,
            headers=headers,
            stream=True
        )
        print(f"Streaming response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ Streaming response:")
            content_parts = []
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data = line[6:]
                        if data == '[DONE]':
                            break
                        try:
                            chunk = json.loads(data)
                            if 'choices' in chunk and chunk['choices']:
                                delta = chunk['choices'][0].get('delta', {})
                                content = delta.get('content', '')
                                if content:
                                    content_parts.append(content)
                                    print(content, end='', flush=True)
                        except json.JSONDecodeError:
                            continue
            
            print(f"\n✓ Streaming completed. Total content: {''.join(content_parts)[:100]}...")
            return True
        else:
            print(f"✗ Streaming failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Streaming error: {e}")
        return False

def test_alternative_models():
    """Test alternative models"""
    print("\n3. Testing alternative models...")
    
    token = get_auth_token()
    if not token:
        print("✗ Could not get authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test with dialogpt-small
    for model_id in ["dialogpt-small", "gpt2"]:
        print(f"\nTesting {model_id}...")
        
        chat_request = {
            "model": model_id,
            "messages": [
                {"role": "user", "content": "Hi there!"}
            ],
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 30
        }
        
        try:
            response = requests.post(
                f"{API_BASE}/inference/chat/completions",
                json=chat_request,
                headers=headers
            )
            print(f"{model_id} response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if 'choices' in data and data['choices']:
                    content = data['choices'][0]['message']['content']
                    print(f"✓ {model_id} response: {content[:50]}...")
                else:
                    print(f"✗ Invalid response format from {model_id}")
            else:
                print(f"✗ {model_id} failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"✗ {model_id} error: {e}")

def main():
    """Run all tests"""
    print("SmallDoge WebUI Final Integration Test")
    print("=" * 50)
    
    # Test 1: Model list
    model_list_success = test_model_list()
    
    # Test 2: Doge chat
    doge_chat_success = test_doge_chat()
    
    # Test 3: Streaming chat
    streaming_success = test_streaming_chat()
    
    # Test 4: Alternative models
    test_alternative_models()
    
    print("\n" + "=" * 50)
    print("FINAL TEST RESULTS:")
    print("=" * 50)
    print(f"Model listing: {'✓' if model_list_success else '✗'}")
    print(f"Doge-160M chat: {'✓' if doge_chat_success else '✗'}")
    print(f"Streaming chat: {'✓' if streaming_success else '✗'}")
    
    if model_list_success and doge_chat_success and streaming_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("SmallDoge WebUI is working correctly!")
        print("\nYou can now:")
        print("  - Use the frontend to chat with models")
        print("  - Create new models")
        print("  - Enjoy streaming conversations")
    else:
        print("\n❌ Some tests failed.")
        print("Check the error messages above for details.")
    
    return model_list_success and doge_chat_success and streaming_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
