#!/usr/bin/env python3
"""
Test script for Pydantic model validation
"""

import sys
import os
import json
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_basic_pydantic():
    """Test basic Pydantic functionality"""
    print("Testing basic Pydantic functionality...")
    
    try:
        from pydantic import BaseModel, Field
        
        class TestModel(BaseModel):
            id: str = Field(..., min_length=1)
            name: str
            
        # Test creation
        test_obj = TestModel(id="test", name="Test Name")
        print("✓ Basic Pydantic model works")
        print(f"  ID: {test_obj.id}")
        print(f"  Name: {test_obj.name}")
        
        # Test attribute access
        print(f"  Has id attribute: {hasattr(test_obj, 'id')}")
        print(f"  ID via getattr: {getattr(test_obj, 'id', 'NOT_FOUND')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic Pydantic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_config():
    """Test ModelConfig"""
    print("\nTesting ModelConfig...")
    
    try:
        from smalldoge_webui.models.models import ModelConfig
        
        # Test with default values
        config1 = ModelConfig()
        print("✓ ModelConfig with defaults works")
        print(f"  Temperature: {config1.temperature}")
        print(f"  Max length: {config1.max_length}")
        
        # Test with custom values
        config2 = ModelConfig(
            max_length=2048,
            temperature=0.7,
            top_p=0.9,
            top_k=50,
            repetition_penalty=1.1,
            do_sample=True,
            load_in_8bit=False,
            load_in_4bit=False,
            device_map="auto",
            torch_dtype="auto",
            trust_remote_code=False
        )
        print("✓ ModelConfig with custom values works")
        print(f"  Temperature: {config2.temperature}")
        print(f"  Max length: {config2.max_length}")
        
        return True
        
    except Exception as e:
        print(f"✗ ModelConfig test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_create():
    """Test ModelCreate"""
    print("\nTesting ModelCreate...")
    
    try:
        from smalldoge_webui.models.models import ModelCreate, ModelConfig
        
        # Create config first
        config = ModelConfig(
            max_length=2048,
            temperature=0.7,
            top_p=0.9,
            top_k=50,
            repetition_penalty=1.1,
            do_sample=True,
            load_in_8bit=False,
            load_in_4bit=False,
            device_map="auto",
            torch_dtype="auto",
            trust_remote_code=False
        )
        
        # Test ModelCreate
        model_create = ModelCreate(
            id="test-model",
            name="Test Model",
            description="A test model",
            model_type="transformers",
            model_path="test/path",
            config=config,
            is_public=True
        )
        
        print("✓ ModelCreate works")
        print(f"  ID: {model_create.id}")
        print(f"  Name: {model_create.name}")
        print(f"  Type: {model_create.model_type}")
        print(f"  Has id attribute: {hasattr(model_create, 'id')}")
        print(f"  ID via getattr: {getattr(model_create, 'id', 'NOT_FOUND')}")
        
        # Test model_dump
        model_dict = model_create.model_dump()
        print("✓ model_dump() works")
        print(f"  Dict keys: {list(model_dict.keys())}")
        print(f"  Dict id: {model_dict.get('id', 'NOT_FOUND')}")
        
        return True
        
    except Exception as e:
        print(f"✗ ModelCreate test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_parsing():
    """Test JSON parsing to ModelCreate"""
    print("\nTesting JSON parsing to ModelCreate...")
    
    try:
        from smalldoge_webui.models.models import ModelCreate
        
        # Test data exactly as sent from frontend
        json_data = {
            "id": "doge-160m",
            "name": "Doge-160M",
            "description": "Doge-160M - transformers model",
            "model_type": "transformers",
            "model_path": "SmallDoge/Doge-160M",
            "config": {
                "max_length": 2048,
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 50,
                "repetition_penalty": 1.1,
                "do_sample": True,
                "load_in_8bit": False,
                "load_in_4bit": False,
                "device_map": "auto",
                "torch_dtype": "auto",
                "trust_remote_code": False
            },
            "is_public": True
        }
        
        print(f"Input JSON: {json.dumps(json_data, indent=2)}")
        
        # Test parsing
        model_create = ModelCreate(**json_data)
        print("✓ JSON parsing to ModelCreate works")
        print(f"  Parsed ID: {model_create.id}")
        print(f"  Parsed name: {model_create.name}")
        print(f"  Has id attribute: {hasattr(model_create, 'id')}")
        
        # Test accessing id in different ways
        print(f"  Direct access: {model_create.id}")
        print(f"  Getattr access: {getattr(model_create, 'id', 'NOT_FOUND')}")
        print(f"  Dict access: {model_create.__dict__.get('id', 'NOT_FOUND')}")
        
        return True
        
    except Exception as e:
        print(f"✗ JSON parsing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fastapi_validation():
    """Test FastAPI-style validation"""
    print("\nTesting FastAPI-style validation...")
    
    try:
        from smalldoge_webui.models.models import ModelCreate
        from pydantic import ValidationError
        
        # Test with missing required field
        try:
            ModelCreate(name="Test", model_type="transformers", model_path="test")
            print("✗ Should have failed with missing id")
            return False
        except ValidationError as e:
            print("✓ Validation correctly catches missing id")
            print(f"  Error: {e}")
        
        # Test with valid data
        model_create = ModelCreate(
            id="test",
            name="Test",
            model_type="transformers",
            model_path="test"
        )
        print("✓ Validation passes with valid data")
        print(f"  ID: {model_create.id}")
        
        return True
        
    except Exception as e:
        print(f"✗ FastAPI validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("SmallDoge WebUI Pydantic Model Test")
    print("=" * 50)
    
    tests = [
        test_basic_pydantic,
        test_model_config,
        test_model_create,
        test_json_parsing,
        test_fastapi_validation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Pydantic models are working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
