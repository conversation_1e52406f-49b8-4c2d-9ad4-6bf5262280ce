# 🐕 SmallDoge WebUI

基于 open-webui 架构的 AI 模型推理平台，使用 FastAPI 后端 + Gradio 前端，支持 HuggingFace Transformers 模型。

## ✨ 功能特性

### 🚀 核心功能
- **FastAPI 后端**: 高性能异步 Web 框架，完全兼容 open-webui API
- **Gradio 前端**: 现代化 Web 界面，响应式设计
- **Transformers 推理**: 支持 HuggingFace Transformers 模型
- **OpenAI 兼容**: 兼容 OpenAI Chat Completions API

### 🔐 认证系统
- JWT 令牌认证
- API Key 支持
- 用户角色管理
- 会话管理

### 🤖 模型管理
- 动态模型加载/卸载
- 多种模型类型支持
- 模型配置管理
- 实时状态监控

### 💬 对话功能
- 实时流式对话
- 对话历史管理
- 多轮对话支持
- 自定义参数

## 🏗️ 架构设计

```
SmallDoge WebUI
├── backend/                 # FastAPI 后端
│   ├── smalldoge_webui/    # 主要代码
│   │   ├── main.py         # 应用入口
│   │   ├── models/         # 数据模型
│   │   ├── routers/        # API 路由
│   │   ├── utils/          # 工具函数
│   │   └── internal/       # 内部模块
│   ├── requirements.txt    # 后端依赖
│   └── start_windows.bat   # 后端启动脚本
├── frontend/               # Gradio 前端
│   ├── app.py             # 前端应用
│   ├── requirements.txt   # 前端依赖
│   └── start_frontend.bat # 前端启动脚本
├── start_all.bat          # 一键启动脚本
└── README.md              # 项目说明
```

## 🚀 快速开始

### 方式1: 一键启动 (推荐)

```bash
# 克隆项目后，直接运行
start_all.bat
```

这个脚本会自动：
1. 创建虚拟环境
2. 安装所有依赖
3. 初始化数据库
4. 启动后端和前端服务
5. 打开浏览器

### 方式2: 分步启动

#### 1. 启动后端
```bash
cd backend
start_windows.bat
```

#### 2. 启动前端
```bash
cd frontend
start_frontend.bat
```

### 3. 访问应用

- **前端界面**: http://localhost:7860
- **后端 API**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs

## 📖 使用指南

### 首次使用

1. **注册账户**
   - 访问 http://localhost:7860
   - 点击"注册"标签
   - 填写用户信息并注册

2. **添加模型**
   - 登录后进入"模型管理"标签
   - 添加 HuggingFace 模型，例如:
     - 模型ID: `gpt2`
     - 模型名称: `GPT-2`
     - 模型路径: `gpt2`
     - 模型类型: `transformers`

3. **开始对话**
   - 进入"对话"标签
   - 选择刚添加的模型
   - 开始与 AI 对话

### 支持的模型类型

#### HuggingFace Transformers
```
模型路径示例:
- gpt2
- microsoft/DialoGPT-medium
- facebook/blenderbot-400M-distill
- google/flan-t5-base
```

#### 本地模型
```
模型路径示例:
- ./models/my-model
- /path/to/local/model
```

## 🔧 配置说明

### 后端配置 (backend/.env)
```bash
# 数据库
DATABASE_URL=sqlite:///./data/smalldoge.db

# 认证
WEBUI_SECRET_KEY=your-secret-key
ENABLE_SIGNUP=true

# 模型
DEVICE=auto
MAX_CONCURRENT_REQUESTS=10

# 服务器
HOST=0.0.0.0
PORT=8000
```

### 前端配置 (frontend/.env)
```bash
# 后端连接
BACKEND_URL=http://localhost:8000

# Gradio 设置
GRADIO_SERVER_PORT=7860
GRADIO_SHARE=false
```

## 🔌 API 接口

### 认证 API
```bash
# 用户注册
POST /api/v1/auth/signup

# 用户登录
POST /api/v1/auth/signin

# 获取用户信息
GET /api/v1/auth/me
```

### 模型 API
```bash
# 获取模型列表
GET /api/v1/models

# 创建模型
POST /api/v1/models

# 加载模型
POST /api/v1/models/{model_id}/load
```

### 推理 API (OpenAI 兼容)
```bash
# 对话完成
POST /api/v1/inference/chat/completions

# 文本嵌入
POST /api/v1/inference/embeddings
```

## 🛠️ 开发指南

### 环境要求
- Python 3.8+
- Windows 10/11
- 8GB+ RAM (推荐 16GB+)
- GPU (可选，用于加速)

### 开发模式启动

#### 后端开发
```bash
cd backend
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
python -m smalldoge_webui.main
```

#### 前端开发
```bash
cd frontend
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
python app.py
```

### 添加新功能

1. **后端 API**: 在 `backend/smalldoge_webui/routers/` 添加新路由
2. **前端界面**: 在 `frontend/app.py` 添加新组件
3. **数据模型**: 在 `backend/smalldoge_webui/models/` 添加新模型

## 🐛 故障排除

### 常见问题

1. **后端启动失败**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :8000
   
   # 检查依赖安装
   pip install -r requirements.txt
   ```

2. **前端连接失败**
   ```bash
   # 确认后端运行
   curl http://localhost:8000/health
   
   # 检查防火墙设置
   ```

3. **模型加载失败**
   ```bash
   # 检查模型路径
   # 确认网络连接 (下载 HuggingFace 模型)
   # 查看后端日志
   ```

### 日志查看

- **后端日志**: 在后端终端窗口查看
- **前端日志**: 在前端终端窗口查看
- **浏览器日志**: F12 开发者工具 Console

## 🚀 部署

### Docker 部署
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 生产环境
1. 使用 PostgreSQL 数据库
2. 配置 Nginx 反向代理
3. 启用 HTTPS
4. 设置环境变量
5. 配置监控和日志

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [open-webui](https://github.com/open-webui/open-webui) - 架构设计参考
- [FastAPI](https://fastapi.tiangolo.com/) - 后端框架
- [Gradio](https://gradio.app/) - 前端框架
- [HuggingFace Transformers](https://huggingface.co/transformers/) - 模型推理

## 📞 支持

如有问题，请：
1. 查看 [故障排除](#-故障排除) 部分
2. 搜索现有 [Issues](../../issues)
3. 创建新的 Issue

---

**SmallDoge WebUI** - 让 AI 对话更简单 🐕✨
