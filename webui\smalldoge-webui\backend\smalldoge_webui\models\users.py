"""
User Models for SmallDoge WebUI
Based on open-webui user model structure
"""

import time
import uuid
from typing import Optional, Dict, Any
from datetime import datetime

from sqlalchemy import Column, String, <PERSON>Inte<PERSON>, <PERSON>olean, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.types import TypeDecorator, VARCHAR
from pydantic import BaseModel, EmailStr, Field, ConfigDict
import json

from smalldoge_webui.internal.db import Base, get_db
from smalldoge_webui.constants import USER_ROLES

# Custom JSON field for SQLAlchemy
class JSONField(TypeDecorator):
    impl = Text
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            return json.dumps(value)
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            return json.loads(value)
        return value

# SQLAlchemy User Model
class User(Base):
    __tablename__ = "users"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    email = Column(String, unique=True, nullable=False, index=True)
    role = Column(String, default=USER_ROLES.USER)
    profile_image_url = Column(Text, nullable=True)
    
    # Timestamps
    last_active_at = Column(BigInteger, default=lambda: int(time.time()))
    updated_at = Column(BigInteger, default=lambda: int(time.time()))
    created_at = Column(BigInteger, default=lambda: int(time.time()))
    
    # API Key for programmatic access
    api_key = Column(String, nullable=True, unique=True)
    
    # User settings and info as JSON
    settings = Column(JSONField, nullable=True)
    info = Column(JSONField, nullable=True)
    
    # OAuth integration
    oauth_sub = Column(Text, unique=True, nullable=True)
    
    # Status flags
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)

# Pydantic Models for API

class UserSettings(BaseModel):
    """User settings model"""
    ui: Optional[Dict[str, Any]] = {}
    preferences: Optional[Dict[str, Any]] = {}
    model_config = ConfigDict(extra="allow")

class UserInfo(BaseModel):
    """User info model"""
    bio: Optional[str] = None
    location: Optional[str] = None
    website: Optional[str] = None
    model_config = ConfigDict(extra="allow")

class UserModel(BaseModel):
    """Base user model for API responses"""
    id: str
    name: str
    email: str
    role: str
    profile_image_url: Optional[str] = None
    last_active_at: int
    updated_at: int
    created_at: int
    api_key: Optional[str] = None
    settings: Optional[UserSettings] = None
    info: Optional[UserInfo] = None
    oauth_sub: Optional[str] = None
    is_active: bool = True
    is_verified: bool = False

    model_config = ConfigDict(from_attributes=True)

class UserCreate(BaseModel):
    """User creation model"""
    name: str = Field(..., min_length=1, max_length=100)
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=128)
    role: Optional[str] = USER_ROLES.USER
    profile_image_url: Optional[str] = None

class UserUpdate(BaseModel):
    """User update model"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    email: Optional[EmailStr] = None
    role: Optional[str] = None
    profile_image_url: Optional[str] = None
    settings: Optional[UserSettings] = None
    info: Optional[UserInfo] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None

class UserResponse(BaseModel):
    """User response model (without sensitive data)"""
    id: str
    name: str
    email: str
    role: str
    profile_image_url: Optional[str] = None
    last_active_at: int
    created_at: int
    is_active: bool
    is_verified: bool

    model_config = ConfigDict(from_attributes=True)

class UserListResponse(BaseModel):
    """User list response model"""
    users: list[UserResponse]
    total: int
    page: int
    per_page: int

# Database Operations Class
class Users:
    """User database operations"""
    
    @staticmethod
    def get_user_by_id(user_id: str) -> Optional[UserModel]:
        """Get user by ID"""
        try:
            with get_db() as db:
                user = db.query(User).filter(User.id == user_id).first()
                return UserModel.model_validate(user) if user else None
        except Exception as e:
            print(f"Error getting user by ID: {e}")
            return None
    
    @staticmethod
    def get_user_by_email(email: str) -> Optional[UserModel]:
        """Get user by email"""
        try:
            with get_db() as db:
                user = db.query(User).filter(User.email == email).first()
                return UserModel.model_validate(user) if user else None
        except Exception as e:
            print(f"Error getting user by email: {e}")
            return None
    
    @staticmethod
    def get_user_by_api_key(api_key: str) -> Optional[UserModel]:
        """Get user by API key"""
        try:
            with get_db() as db:
                user = db.query(User).filter(User.api_key == api_key).first()
                return UserModel.model_validate(user) if user else None
        except Exception as e:
            print(f"Error getting user by API key: {e}")
            return None
    
    @staticmethod
    def create_user(user_data: UserCreate, hashed_password: str) -> Optional[UserModel]:
        """Create new user"""
        try:
            with get_db() as db:
                # Check if email already exists
                existing_user = db.query(User).filter(User.email == user_data.email).first()
                if existing_user:
                    return None
                
                # Create new user
                user = User(
                    name=user_data.name,
                    email=user_data.email,
                    role=user_data.role,
                    profile_image_url=user_data.profile_image_url,
                )
                
                db.add(user)
                db.commit()
                db.refresh(user)
                
                return UserModel.model_validate(user)
        except Exception as e:
            print(f"Error creating user: {e}")
            return None
    
    @staticmethod
    def update_user_by_id(user_id: str, user_data: UserUpdate) -> Optional[UserModel]:
        """Update user by ID"""
        try:
            with get_db() as db:
                user = db.query(User).filter(User.id == user_id).first()
                if not user:
                    return None
                
                # Update fields
                update_data = user_data.model_dump(exclude_unset=True)
                for field, value in update_data.items():
                    if hasattr(user, field):
                        setattr(user, field, value)
                
                user.updated_at = int(time.time())
                
                db.commit()
                db.refresh(user)
                
                return UserModel.model_validate(user)
        except Exception as e:
            print(f"Error updating user: {e}")
            return None
    
    @staticmethod
    def delete_user_by_id(user_id: str) -> bool:
        """Delete user by ID"""
        try:
            with get_db() as db:
                user = db.query(User).filter(User.id == user_id).first()
                if not user:
                    return False
                
                db.delete(user)
                db.commit()
                return True
        except Exception as e:
            print(f"Error deleting user: {e}")
            return False
    
    @staticmethod
    def get_users(skip: int = 0, limit: int = 100) -> list[UserModel]:
        """Get list of users"""
        try:
            with get_db() as db:
                users = db.query(User).offset(skip).limit(limit).all()
                return [UserModel.model_validate(user) for user in users]
        except Exception as e:
            print(f"Error getting users: {e}")
            return []
    
    @staticmethod
    def get_num_users() -> int:
        """Get total number of users"""
        try:
            with get_db() as db:
                return db.query(User).count()
        except Exception as e:
            print(f"Error getting user count: {e}")
            return 0
    
    @staticmethod
    def update_user_last_active_by_id(user_id: str) -> bool:
        """Update user's last active timestamp"""
        try:
            with get_db() as db:
                user = db.query(User).filter(User.id == user_id).first()
                if not user:
                    return False
                
                user.last_active_at = int(time.time())
                db.commit()
                return True
        except Exception as e:
            print(f"Error updating user last active: {e}")
            return False
    
    @staticmethod
    def generate_api_key(user_id: str) -> Optional[str]:
        """Generate new API key for user"""
        try:
            import secrets
            api_key = f"sk-{secrets.token_urlsafe(32)}"
            
            with get_db() as db:
                user = db.query(User).filter(User.id == user_id).first()
                if not user:
                    return None
                
                user.api_key = api_key
                user.updated_at = int(time.time())
                db.commit()
                
                return api_key
        except Exception as e:
            print(f"Error generating API key: {e}")
            return None
    
    @staticmethod
    def revoke_api_key(user_id: str) -> bool:
        """Revoke user's API key"""
        try:
            with get_db() as db:
                user = db.query(User).filter(User.id == user_id).first()
                if not user:
                    return False
                
                user.api_key = None
                user.updated_at = int(time.time())
                db.commit()
                return True
        except Exception as e:
            print(f"Error revoking API key: {e}")
            return False
