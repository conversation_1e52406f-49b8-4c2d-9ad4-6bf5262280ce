"""
Inference Router for SmallDoge WebUI
Handles AI model inference requests with transformers backend
Based on open-webui inference patterns
"""

import logging
import json
from typing import List, Optional, Dict, Any, AsyncGenerator

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from smalldoge_webui.utils.auth import get_verified_user, get_current_user_or_api_key
from smalldoge_webui.utils.inference import ModelInferenceEngine
from smalldoge_webui.models.users import UserModel
from smalldoge_webui.constants import ERROR_MESSAGES, MESSAGE_ROLES
from smalldoge_webui.env import SRC_LOG_LEVELS

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["INFERENCE"])

router = APIRouter()

class ChatMessage(BaseModel):
    """Chat message model"""
    role: str = Field(..., description="Message role: system, user, or assistant")
    content: str = Field(..., description="Message content")
    name: Optional[str] = Field(None, description="Optional name for the message")

class ChatCompletionRequest(BaseModel):
    """Chat completion request model (OpenAI compatible)"""
    model: str = Field(..., description="Model ID to use for completion")
    messages: List[ChatMessage] = Field(..., description="List of messages")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="Sampling temperature")
    max_tokens: Optional[int] = Field(None, ge=1, description="Maximum tokens to generate")
    top_p: Optional[float] = Field(1.0, ge=0.0, le=1.0, description="Nucleus sampling parameter")
    top_k: Optional[int] = Field(None, ge=1, description="Top-k sampling parameter")
    frequency_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="Presence penalty")
    stop: Optional[List[str]] = Field(None, description="Stop sequences")
    stream: Optional[bool] = Field(False, description="Whether to stream the response")
    user: Optional[str] = Field(None, description="User identifier")

class ChatCompletionChoice(BaseModel):
    """Chat completion choice"""
    index: int
    message: ChatMessage
    finish_reason: Optional[str] = None

class ChatCompletionUsage(BaseModel):
    """Token usage information"""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int

class ChatCompletionResponse(BaseModel):
    """Chat completion response (OpenAI compatible)"""
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[ChatCompletionChoice]
    usage: Optional[ChatCompletionUsage] = None

class ChatCompletionChunk(BaseModel):
    """Streaming chat completion chunk"""
    id: str
    object: str = "chat.completion.chunk"
    created: int
    model: str
    choices: List[Dict[str, Any]]

class EmbeddingRequest(BaseModel):
    """Embedding request model"""
    input: List[str] = Field(..., description="Input texts to embed")
    model: str = Field(..., description="Model ID to use for embeddings")
    user: Optional[str] = Field(None, description="User identifier")

class EmbeddingData(BaseModel):
    """Embedding data"""
    object: str = "embedding"
    embedding: List[float]
    index: int

class EmbeddingResponse(BaseModel):
    """Embedding response"""
    object: str = "list"
    data: List[EmbeddingData]
    model: str
    usage: Optional[ChatCompletionUsage] = None

@router.post("/chat/completions")
async def chat_completions(
    request: Request,
    completion_request: ChatCompletionRequest,
    current_user: UserModel = Depends(get_current_user_or_api_key)
):
    """
    Create chat completion (OpenAI compatible endpoint)
    Supports both streaming and non-streaming responses
    """
    try:
        # Debug logging
        log.info(f"Received chat completion request from user {current_user.email}")
        log.info(f"Request model: {completion_request.model}")
        log.info(f"Request messages: {completion_request.messages}")
        log.info(f"Request stream: {completion_request.stream}")

        # Validate messages
        if not completion_request.messages:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Messages cannot be empty"
            )
        
        # Validate message roles
        valid_roles = {MESSAGE_ROLES.SYSTEM, MESSAGE_ROLES.USER, MESSAGE_ROLES.ASSISTANT}
        for message in completion_request.messages:
            if message.role not in valid_roles:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid message role: {message.role}"
                )
        
        # Get inference engine
        inference_engine = ModelInferenceEngine()
        
        # Check if model is available
        model_status = await inference_engine.get_model_status(completion_request.model)
        if not model_status.get("loaded", False):
            # Try to load the model
            load_success = await inference_engine.load_model(completion_request.model)
            if not load_success:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Model {completion_request.model} not found or failed to load"
                )
        
        # Prepare inference parameters
        inference_params = {
            "temperature": completion_request.temperature,
            "max_tokens": completion_request.max_tokens,
            "top_p": completion_request.top_p,
            "top_k": completion_request.top_k,
            "stop": completion_request.stop,
        }
        
        # Remove None values
        inference_params = {k: v for k, v in inference_params.items() if v is not None}
        
        if completion_request.stream:
            # Streaming response
            return StreamingResponse(
                stream_chat_completion(
                    inference_engine,
                    completion_request,
                    inference_params,
                    current_user
                ),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )
        else:
            # Non-streaming response
            response = await generate_chat_completion(
                inference_engine,
                completion_request,
                inference_params,
                current_user
            )
            return response
            
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error in chat completion: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.MODEL_INFERENCE_ERROR
        )

async def generate_chat_completion(
    inference_engine: ModelInferenceEngine,
    request: ChatCompletionRequest,
    params: Dict[str, Any],
    user: UserModel
) -> ChatCompletionResponse:
    """Generate non-streaming chat completion"""
    try:
        # Generate response
        result = await inference_engine.chat_completion(
            model_id=request.model,
            messages=[msg.model_dump() for msg in request.messages],
            **params
        )
        
        # Create response
        import time
        import uuid
        
        response = ChatCompletionResponse(
            id=f"chatcmpl-{uuid.uuid4().hex[:8]}",
            created=int(time.time()),
            model=request.model,
            choices=[
                ChatCompletionChoice(
                    index=0,
                    message=ChatMessage(
                        role=MESSAGE_ROLES.ASSISTANT,
                        content=result.get("content", "")
                    ),
                    finish_reason=result.get("finish_reason", "stop")
                )
            ],
            usage=ChatCompletionUsage(
                prompt_tokens=result.get("prompt_tokens", 0),
                completion_tokens=result.get("completion_tokens", 0),
                total_tokens=result.get("total_tokens", 0)
            )
        )
        
        log.info(f"Chat completion generated for user {user.email} using model {request.model}")
        return response
        
    except Exception as e:
        log.error(f"Error generating chat completion: {e}")
        raise

async def stream_chat_completion(
    inference_engine: ModelInferenceEngine,
    request: ChatCompletionRequest,
    params: Dict[str, Any],
    user: UserModel
) -> AsyncGenerator[str, None]:
    """Generate streaming chat completion"""
    try:
        import time
        import uuid
        
        completion_id = f"chatcmpl-{uuid.uuid4().hex[:8]}"
        created = int(time.time())
        
        # Start streaming
        async for chunk in inference_engine.stream_chat_completion(
            model_id=request.model,
            messages=[msg.model_dump() for msg in request.messages],
            **params
        ):
            # Create streaming chunk
            chunk_response = ChatCompletionChunk(
                id=completion_id,
                created=created,
                model=request.model,
                choices=[{
                    "index": 0,
                    "delta": {
                        "content": chunk.get("content", "")
                    },
                    "finish_reason": chunk.get("finish_reason")
                }]
            )
            
            yield f"data: {chunk_response.model_dump_json()}\n\n"
        
        # Send final chunk
        final_chunk = ChatCompletionChunk(
            id=completion_id,
            created=created,
            model=request.model,
            choices=[{
                "index": 0,
                "delta": {},
                "finish_reason": "stop"
            }]
        )
        
        yield f"data: {final_chunk.model_dump_json()}\n\n"
        yield "data: [DONE]\n\n"
        
        log.info(f"Streaming chat completion completed for user {user.email} using model {request.model}")
        
    except Exception as e:
        log.error(f"Error in streaming chat completion: {e}")
        # Send error chunk
        error_chunk = {
            "error": {
                "message": str(e),
                "type": "inference_error"
            }
        }
        yield f"data: {json.dumps(error_chunk)}\n\n"

@router.post("/embeddings", response_model=EmbeddingResponse)
async def create_embeddings(
    embedding_request: EmbeddingRequest,
    current_user: UserModel = Depends(get_current_user_or_api_key)
):
    """Create embeddings for input texts"""
    try:
        # Validate input
        if not embedding_request.input:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Input texts cannot be empty"
            )
        
        # Get inference engine
        inference_engine = ModelInferenceEngine()
        
        # Generate embeddings
        embeddings = await inference_engine.create_embeddings(
            model_id=embedding_request.model,
            texts=embedding_request.input
        )
        
        # Create response
        embedding_data = [
            EmbeddingData(
                embedding=embedding,
                index=i
            )
            for i, embedding in enumerate(embeddings)
        ]
        
        response = EmbeddingResponse(
            data=embedding_data,
            model=embedding_request.model,
            usage=ChatCompletionUsage(
                prompt_tokens=len(" ".join(embedding_request.input).split()),
                completion_tokens=0,
                total_tokens=len(" ".join(embedding_request.input).split())
            )
        )
        
        log.info(f"Embeddings created for user {current_user.email} using model {embedding_request.model}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Error creating embeddings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.MODEL_INFERENCE_ERROR
        )

@router.get("/models")
async def list_inference_models(current_user: UserModel = Depends(get_verified_user)):
    """List available models for inference"""
    try:
        # Get inference engine
        inference_engine = ModelInferenceEngine()
        
        # Get available models
        models = await inference_engine.list_available_models()
        
        return {
            "object": "list",
            "data": models
        }
        
    except Exception as e:
        log.error(f"Error listing inference models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )
