# FastAPI and web framework
fastapi
uvicorn[standard]
python-multipart

# Database
sqlalchemy
alembic

# Authentication and security
python-jose[cryptography]
passlib[bcrypt]

# Data validation
pydantic
email-validator

# HTTP client
httpx
aiofiles

# Machine Learning and Transformers
torch
transformers
tokenizers
accelerate
bitsandbytes

# Optional ML dependencies
# sentence-transformers==2.2.2
# datasets==2.14.6

# Gradio for frontend
gradio

# Utilities
numpy
pandas
python-dotenv

# Development and testing
pytest
pytest-asyncio
black
flake8

# Optional: Redis for caching
# redis==5.0.1

# Optional: PostgreSQL driver
# psycopg2-binary==2.9.9

# Optional: MySQL driver  
# pymysql==1.1.0
