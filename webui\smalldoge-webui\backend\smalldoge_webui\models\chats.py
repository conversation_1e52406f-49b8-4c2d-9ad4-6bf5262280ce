"""
Chat Models for SmallDoge WebUI
Based on open-webui chat structure
"""

import time
import uuid
from typing import Optional, List, Dict, Any
from datetime import datetime

from sqlalchemy import Column, String, BigInteger, Boolean, Text
from pydantic import BaseModel, Field, ConfigDict

from smalldoge_webui.internal.db import Base, get_db
from smalldoge_webui.models.users import J<PERSON><PERSON>ield
from smalldoge_webui.constants import MESSAGE_ROLES

# SQLAlchemy Chat Model
class Chat(Base):
    __tablename__ = "chats"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String, nullable=False)
    user_id = Column(String, nullable=False, index=True)
    
    # Chat metadata
    model_id = Column(String, nullable=True)  # AI model used
    system_prompt = Column(Text, nullable=True)
    
    # Chat messages as JSON array
    messages = Column(JSONField, nullable=True, default=list)
    
    # Chat settings
    settings = Column(JSONField, nullable=True)
    
    # Status and metadata
    is_archived = Column(Boolean, default=False)
    is_pinned = Column(Boolean, default=False)
    is_shared = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(BigInteger, default=lambda: int(time.time()))
    updated_at = Column(BigInteger, default=lambda: int(time.time()))
    
    # Statistics
    message_count = Column(BigInteger, default=0)
    total_tokens = Column(BigInteger, default=0)

# Pydantic Models for API

class ChatMessage(BaseModel):
    """Chat message model"""
    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    role: str = Field(..., description="Message role: system, user, or assistant")
    content: str = Field(..., description="Message content")
    timestamp: Optional[int] = Field(default_factory=lambda: int(time.time()))
    model: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = {}

    model_config = ConfigDict(extra="allow")

class ChatSettings(BaseModel):
    """Chat settings model"""
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 2048
    top_p: Optional[float] = 0.9
    top_k: Optional[int] = 50
    repetition_penalty: Optional[float] = 1.1
    system_prompt: Optional[str] = None
    
    model_config = ConfigDict(extra="allow")

class ChatModel(BaseModel):
    """Base chat model for API responses"""
    id: str
    title: str
    user_id: str
    model_id: Optional[str] = None
    system_prompt: Optional[str] = None
    messages: List[ChatMessage] = []
    settings: Optional[ChatSettings] = None
    is_archived: bool = False
    is_pinned: bool = False
    is_shared: bool = False
    created_at: int
    updated_at: int
    message_count: int = 0
    total_tokens: int = 0

    model_config = ConfigDict(from_attributes=True)

class ChatCreate(BaseModel):
    """Chat creation model"""
    title: str = Field(..., min_length=1, max_length=200)
    model_id: Optional[str] = None
    system_prompt: Optional[str] = None
    settings: Optional[ChatSettings] = None

class ChatUpdate(BaseModel):
    """Chat update model"""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    model_id: Optional[str] = None
    system_prompt: Optional[str] = None
    settings: Optional[ChatSettings] = None
    is_archived: Optional[bool] = None
    is_pinned: Optional[bool] = None
    is_shared: Optional[bool] = None

class ChatResponse(BaseModel):
    """Chat response model (without messages for list views)"""
    id: str
    title: str
    model_id: Optional[str] = None
    is_archived: bool = False
    is_pinned: bool = False
    is_shared: bool = False
    created_at: int
    updated_at: int
    message_count: int = 0

    model_config = ConfigDict(from_attributes=True)

class ChatListResponse(BaseModel):
    """Chat list response model"""
    chats: List[ChatResponse]
    total: int
    page: int
    per_page: int

class MessageCreate(BaseModel):
    """Message creation model"""
    role: str = Field(..., description="Message role")
    content: str = Field(..., description="Message content")
    model: Optional[str] = None

# Database Operations Class
class Chats:
    """Chat database operations"""
    
    @staticmethod
    def get_chat_by_id(chat_id: str) -> Optional[ChatModel]:
        """Get chat by ID"""
        try:
            with get_db() as db:
                chat = db.query(Chat).filter(Chat.id == chat_id).first()
                if not chat:
                    return None
                
                # Convert messages to ChatMessage objects
                messages = []
                if chat.messages:
                    for msg in chat.messages:
                        messages.append(ChatMessage(**msg))
                
                chat_data = ChatModel.model_validate(chat)
                chat_data.messages = messages
                return chat_data
        except Exception as e:
            print(f"Error getting chat by ID: {e}")
            return None
    
    @staticmethod
    def create_chat(chat_data: ChatCreate, user_id: str) -> Optional[ChatModel]:
        """Create new chat"""
        try:
            with get_db() as db:
                chat = Chat(
                    title=chat_data.title,
                    user_id=user_id,
                    model_id=chat_data.model_id,
                    system_prompt=chat_data.system_prompt,
                    settings=chat_data.settings.model_dump() if chat_data.settings else None,
                    messages=[]
                )
                
                db.add(chat)
                db.commit()
                db.refresh(chat)
                
                return ChatModel.model_validate(chat)
        except Exception as e:
            print(f"Error creating chat: {e}")
            return None
    
    @staticmethod
    def update_chat_by_id(chat_id: str, chat_data: ChatUpdate) -> Optional[ChatModel]:
        """Update chat by ID"""
        try:
            with get_db() as db:
                chat = db.query(Chat).filter(Chat.id == chat_id).first()
                if not chat:
                    return None
                
                # Update fields
                update_data = chat_data.model_dump(exclude_unset=True)
                for field, value in update_data.items():
                    if hasattr(chat, field):
                        if field == "settings" and value:
                            setattr(chat, field, value.model_dump() if hasattr(value, 'model_dump') else value)
                        else:
                            setattr(chat, field, value)
                
                chat.updated_at = int(time.time())
                
                db.commit()
                db.refresh(chat)
                
                return ChatModel.model_validate(chat)
        except Exception as e:
            print(f"Error updating chat: {e}")
            return None
    
    @staticmethod
    def delete_chat_by_id(chat_id: str) -> bool:
        """Delete chat by ID"""
        try:
            with get_db() as db:
                chat = db.query(Chat).filter(Chat.id == chat_id).first()
                if not chat:
                    return False
                
                db.delete(chat)
                db.commit()
                return True
        except Exception as e:
            print(f"Error deleting chat: {e}")
            return False
    
    @staticmethod
    def get_chats_by_user_id(user_id: str, skip: int = 0, limit: int = 100, 
                            archived: Optional[bool] = None) -> List[ChatResponse]:
        """Get chats by user ID"""
        try:
            with get_db() as db:
                query = db.query(Chat).filter(Chat.user_id == user_id)
                
                if archived is not None:
                    query = query.filter(Chat.is_archived == archived)
                
                chats = query.order_by(Chat.updated_at.desc()).offset(skip).limit(limit).all()
                return [ChatResponse.model_validate(chat) for chat in chats]
        except Exception as e:
            print(f"Error getting chats by user ID: {e}")
            return []
    
    @staticmethod
    def get_chats_count_by_user_id(user_id: str, archived: Optional[bool] = None) -> int:
        """Get chat count by user ID"""
        try:
            with get_db() as db:
                query = db.query(Chat).filter(Chat.user_id == user_id)
                
                if archived is not None:
                    query = query.filter(Chat.is_archived == archived)
                
                return query.count()
        except Exception as e:
            print(f"Error getting chat count: {e}")
            return 0
    
    @staticmethod
    def add_message_to_chat(chat_id: str, message: MessageCreate) -> Optional[ChatMessage]:
        """Add message to chat"""
        try:
            with get_db() as db:
                chat = db.query(Chat).filter(Chat.id == chat_id).first()
                if not chat:
                    return None
                
                # Create new message
                new_message = ChatMessage(
                    role=message.role,
                    content=message.content,
                    model=message.model
                )
                
                # Add to messages list
                if not chat.messages:
                    chat.messages = []
                
                chat.messages.append(new_message.model_dump())
                chat.message_count = len(chat.messages)
                chat.updated_at = int(time.time())
                
                db.commit()
                return new_message
        except Exception as e:
            print(f"Error adding message to chat: {e}")
            return None
    
    @staticmethod
    def update_chat_messages(chat_id: str, messages: List[ChatMessage]) -> bool:
        """Update all messages in chat"""
        try:
            with get_db() as db:
                chat = db.query(Chat).filter(Chat.id == chat_id).first()
                if not chat:
                    return False
                
                chat.messages = [msg.model_dump() for msg in messages]
                chat.message_count = len(messages)
                chat.updated_at = int(time.time())
                
                db.commit()
                return True
        except Exception as e:
            print(f"Error updating chat messages: {e}")
            return False
    
    @staticmethod
    def archive_chat(chat_id: str) -> bool:
        """Archive chat"""
        try:
            with get_db() as db:
                chat = db.query(Chat).filter(Chat.id == chat_id).first()
                if not chat:
                    return False
                
                chat.is_archived = True
                chat.updated_at = int(time.time())
                
                db.commit()
                return True
        except Exception as e:
            print(f"Error archiving chat: {e}")
            return False
    
    @staticmethod
    def unarchive_chat(chat_id: str) -> bool:
        """Unarchive chat"""
        try:
            with get_db() as db:
                chat = db.query(Chat).filter(Chat.id == chat_id).first()
                if not chat:
                    return False
                
                chat.is_archived = False
                chat.updated_at = int(time.time())
                
                db.commit()
                return True
        except Exception as e:
            print(f"Error unarchiving chat: {e}")
            return False
    
    @staticmethod
    def toggle_chat_pin(chat_id: str) -> bool:
        """Toggle chat pin status"""
        try:
            with get_db() as db:
                chat = db.query(Chat).filter(Chat.id == chat_id).first()
                if not chat:
                    return False
                
                chat.is_pinned = not chat.is_pinned
                chat.updated_at = int(time.time())
                
                db.commit()
                return True
        except Exception as e:
            print(f"Error toggling chat pin: {e}")
            return False
