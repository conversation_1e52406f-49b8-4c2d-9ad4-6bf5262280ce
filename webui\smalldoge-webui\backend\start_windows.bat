@echo off
REM SmallDoge WebUI Backend Startup Script for Windows
REM Based on open-webui startup patterns

echo Starting SmallDoge WebUI Backend...
echo.

REM Set environment variables
set PYTHONPATH=%CD%
set ENV=dev
set DEBUG=true

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to your PATH
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Error: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

REM Create data directories
echo Creating data directories...
if not exist "data" mkdir data
if not exist "data\models" mkdir data\models
if not exist "data\uploads" mkdir data\uploads
if not exist "data\static" mkdir data\static

REM Initialize database
echo Initializing database...
python -c "from smalldoge_webui.internal.db import init_db; init_db()"
if errorlevel 1 (
    echo Warning: Database initialization failed, will retry on startup
)

REM Start the server
echo.
echo Starting SmallDoge WebUI Backend Server...
echo Server will be available at: http://localhost:8000
echo API documentation: http://localhost:8000/docs
echo.
echo Press Ctrl+C to stop the server
echo.

python -m smalldoge_webui.main

REM Pause on exit to see any error messages
if errorlevel 1 (
    echo.
    echo Server stopped with error code %errorlevel%
    pause
)
