# SmallDoge WebUI Frontend

基于 Gradio 的 SmallDoge WebUI 前端应用，提供直观的 Web 界面与后端 API 交互。

## 功能特性

- 🎨 **现代化界面**: 基于 Gradio 的响应式 Web 界面
- 🔐 **用户认证**: 完整的登录/注册系统
- 💬 **实时对话**: 支持流式对话响应
- 🤖 **模型管理**: 可视化模型配置和管理
- ⚙️ **系统设置**: 后端连接和用户信息管理
- 📱 **响应式设计**: 支持桌面和移动设备

## 界面预览

### 主要功能模块

1. **用户认证**
   - 用户登录/注册
   - 密码验证
   - 会话管理

2. **AI 对话**
   - 模型选择
   - 实时对话
   - 对话历史
   - 流式响应

3. **模型管理**
   - 添加新模型
   - 模型配置
   - 模型类型选择

4. **系统设置**
   - 后端连接状态
   - 用户信息显示
   - 连接测试

## 快速开始

### 1. 环境要求

- Python 3.8+
- 运行中的 SmallDoge WebUI 后端 (http://localhost:8000)

### 2. 安装依赖

```bash
# 进入前端目录
cd frontend

# 创建虚拟环境 (可选)
python -m venv venv
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置环境

```bash
# 复制环境配置文件
copy .env.example .env

# 编辑 .env 文件 (可选)
# BACKEND_URL=http://localhost:8000
# GRADIO_SERVER_PORT=7860
```

### 4. 启动前端

#### 方式1: 使用启动脚本 (推荐)
```bash
start_frontend.bat
```

#### 方式2: 直接运行
```bash
python app.py
```

### 5. 访问应用

打开浏览器访问: http://localhost:7860

## 使用指南

### 首次使用

1. **启动后端服务**
   ```bash
   cd ../backend
   start_windows.bat
   ```

2. **启动前端服务**
   ```bash
   cd frontend
   start_frontend.bat
   ```

3. **注册账户**
   - 访问 http://localhost:7860
   - 点击"注册"标签
   - 填写用户信息并注册

4. **开始对话**
   - 登录成功后进入主界面
   - 选择或添加模型
   - 开始与 AI 对话

### 模型管理

#### 添加 HuggingFace 模型
1. 进入"模型管理"标签
2. 填写模型信息:
   - **模型ID**: `gpt2-medium`
   - **模型名称**: `GPT-2 Medium`
   - **模型路径**: `gpt2-medium`
   - **模型类型**: `transformers`
3. 点击"创建模型"

#### 添加本地模型
1. 填写模型信息:
   - **模型ID**: `my-local-model`
   - **模型名称**: `我的本地模型`
   - **模型路径**: `./models/my-model`
   - **模型类型**: `local`
2. 点击"创建模型"

### 对话功能

1. **选择模型**: 在下拉菜单中选择要使用的模型
2. **发送消息**: 在输入框中输入消息，按回车或点击"发送"
3. **查看响应**: AI 会实时流式返回响应
4. **清空对话**: 点击"清空对话"按钮重新开始

## 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `BACKEND_URL` | `http://localhost:8000` | 后端 API 地址 |
| `GRADIO_SERVER_NAME` | `0.0.0.0` | 前端服务器地址 |
| `GRADIO_SERVER_PORT` | `7860` | 前端服务器端口 |
| `GRADIO_SHARE` | `false` | 是否创建公共链接 |
| `GRADIO_DEBUG` | `true` | 调试模式 |

### 自定义主题

可以通过修改 `app.py` 中的主题设置来自定义界面:

```python
with gr.Blocks(
    title="SmallDoge WebUI",
    theme=gr.themes.Soft(),  # 可选: Base, Default, Soft, Monochrome
    css="custom.css"  # 自定义 CSS
) as app:
```

## API 集成

前端通过以下 API 端点与后端通信:

### 认证 API
- `POST /api/v1/auth/signup` - 用户注册
- `POST /api/v1/auth/signin` - 用户登录

### 模型 API
- `GET /api/v1/models` - 获取模型列表
- `POST /api/v1/models` - 创建模型配置

### 推理 API
- `POST /api/v1/inference/chat/completions` - 对话完成

### 系统 API
- `GET /health` - 健康检查

## 故障排除

### 常见问题

1. **无法连接后端**
   - 确认后端服务正在运行
   - 检查 `BACKEND_URL` 配置
   - 查看防火墙设置

2. **登录失败**
   - 检查用户名和密码
   - 确认后端认证服务正常
   - 查看浏览器控制台错误

3. **模型加载失败**
   - 确认模型路径正确
   - 检查模型类型配置
   - 查看后端日志

4. **对话无响应**
   - 确认模型已正确加载
   - 检查网络连接
   - 查看后端推理服务状态

### 调试模式

启用调试模式查看详细信息:

```bash
# 设置环境变量
set GRADIO_DEBUG=true

# 启动应用
python app.py
```

### 日志查看

前端日志会显示在终端中，包括:
- API 请求/响应
- 错误信息
- 用户操作

## 开发指南

### 项目结构

```
frontend/
├── app.py                 # 主应用文件
├── requirements.txt       # 依赖包
├── start_frontend.bat    # Windows 启动脚本
├── .env.example          # 环境配置示例
└── README.md             # 说明文档
```

### 添加新功能

1. **添加新的 API 调用**:
   ```python
   def new_api_call(self, data):
       response = self.session.post(f"{API_BASE}/new-endpoint", json=data)
       return response.json()
   ```

2. **添加新的界面组件**:
   ```python
   with gr.Tab("新功能"):
       new_input = gr.Textbox(label="输入")
       new_button = gr.Button("提交")
       new_output = gr.Markdown("")
   ```

3. **添加事件处理**:
   ```python
   new_button.click(
       new_function,
       inputs=[new_input],
       outputs=[new_output]
   )
   ```

### 自定义样式

可以通过 CSS 自定义界面样式:

```python
css = """
.custom-container {
    background-color: #f0f0f0;
    padding: 20px;
    border-radius: 10px;
}
"""

with gr.Blocks(css=css) as app:
    # 界面组件
```

## 部署

### 生产环境部署

1. **设置环境变量**:
   ```bash
   set GRADIO_SHARE=false
   set GRADIO_DEBUG=false
   set BACKEND_URL=https://your-backend-domain.com
   ```

2. **使用 WSGI 服务器**:
   ```bash
   pip install gunicorn
   gunicorn -w 4 -b 0.0.0.0:7860 app:app
   ```

3. **反向代理配置** (Nginx):
   ```nginx
   location / {
       proxy_pass http://localhost:7860;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
   }
   ```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
