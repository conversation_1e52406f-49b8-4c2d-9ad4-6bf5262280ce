"""
SmallDoge WebUI - AI Model Inference Backend
Based on open-webui architecture with transformers inference backend
"""

import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

__version__ = "1.0.0-dev"
__author__ = "SmallDoge Team"
__description__ = "AI Model Inference WebUI Backend with Transformers"
