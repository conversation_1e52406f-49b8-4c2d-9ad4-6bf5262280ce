@echo off
REM SmallDoge WebUI Backend Startup Script for Windows
REM Based on open-webui startup script

echo Starting SmallDoge WebUI Backend...

REM Get script directory
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

REM Default values
if not defined HOST set HOST=0.0.0.0
if not defined PORT set PORT=8000
if not defined ENV set ENV=dev

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt

REM Set Python path
set PYTHONPATH=%PYTHONPATH%;%cd%

REM Initialize database
echo Initializing database...
python -c "from smalldoge_webui.internal.db import init_db; init_db(); print('Database initialized successfully')"

if errorlevel 1 (
    echo Database initialization failed
    pause
    exit /b 1
)

REM Check for WEBUI_SECRET_KEY
if not defined WEBUI_SECRET_KEY (
    echo Warning: WEBUI_SECRET_KEY not set. Using auto-generated key.
    echo For production, please set WEBUI_SECRET_KEY environment variable.
)

REM Start the server
echo Starting server on %HOST%:%PORT%...
echo Environment: %ENV%
echo Data directory: %DATA_DIR%

if "%ENV%"=="dev" (
    echo Running in development mode with auto-reload...
    uvicorn smalldoge_webui.main:app --host %HOST% --port %PORT% --reload --log-level debug --access-log
) else (
    echo Running in production mode...
    if not defined UVICORN_WORKERS set UVICORN_WORKERS=1
    echo Using %UVICORN_WORKERS% workers
    uvicorn smalldoge_webui.main:app --host %HOST% --port %PORT% --workers %UVICORN_WORKERS% --log-level info --access-log
)

pause
