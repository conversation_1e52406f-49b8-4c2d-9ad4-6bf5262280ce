#!/usr/bin/env python3
"""
Simple test to verify basic setup
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

print("SmallDoge WebUI Simple Test")
print("=" * 40)

# Test 1: Basic Python setup
print("1. Testing Python setup...")
print(f"   Python version: {sys.version}")
print(f"   Current directory: {current_dir}")
print("   ✓ Python setup OK")

# Test 2: Import constants
print("\n2. Testing constants import...")
try:
    from smalldoge_webui.constants import VERSION, ERROR_MESSAGES
    print(f"   Version: {VERSION}")
    print("   ✓ Constants import OK")
except Exception as e:
    print(f"   ✗ Constants import failed: {e}")
    sys.exit(1)

# Test 3: Import environment
print("\n3. Testing environment import...")
try:
    from smalldoge_webui.env import VERSION as ENV_VERSION, DATA_DIR
    print(f"   Environment version: {ENV_VERSION}")
    print(f"   Data directory: {DATA_DIR}")
    print("   ✓ Environment import OK")
except Exception as e:
    print(f"   ✗ Environment import failed: {e}")
    sys.exit(1)

# Test 4: Check if data directory was created
print("\n4. Testing data directory creation...")
if DATA_DIR.exists():
    print(f"   Data directory exists: {DATA_DIR}")
    print("   ✓ Data directory OK")
else:
    print(f"   ✗ Data directory not found: {DATA_DIR}")

# Test 5: Test database module (without dependencies)
print("\n5. Testing database module structure...")
try:
    # Just test if the file exists and can be parsed
    db_file = current_dir / "smalldoge_webui" / "internal" / "db.py"
    if db_file.exists():
        print("   Database module file exists")
        print("   ✓ Database module structure OK")
    else:
        print("   ✗ Database module file not found")
except Exception as e:
    print(f"   ✗ Database module test failed: {e}")

print("\n" + "=" * 40)
print("✅ Basic setup verification completed!")
print("\nNext steps:")
print("1. Install dependencies: pip install -r requirements.txt")
print("2. Run full test: python test_setup.py")
print("3. Start server: python -m smalldoge_webui.main")
