#!/bin/bash

# SmallDoge WebUI Backend Startup Script
# Based on open-webui startup script

set -e

echo "Starting SmallDoge WebUI Backend..."

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Default values
HOST="${HOST:-0.0.0.0}"
PORT="${PORT:-8000}"
ENV="${ENV:-dev}"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Set Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Initialize database
echo "Initializing database..."
python -c "
from smalldoge_webui.internal.db import init_db
try:
    init_db()
    print('Database initialized successfully')
except Exception as e:
    print(f'Database initialization failed: {e}')
    exit(1)
"

# Check for WEBUI_SECRET_KEY
if [ -z "$WEBUI_SECRET_KEY" ]; then
    echo "Warning: WEBUI_SECRET_KEY not set. Using auto-generated key."
    echo "For production, please set WEBUI_SECRET_KEY environment variable."
fi

# Start the server
echo "Starting server on $HOST:$PORT..."
echo "Environment: $ENV"
echo "Data directory: ${DATA_DIR:-./data}"

if [ "$ENV" = "dev" ]; then
    echo "Running in development mode with auto-reload..."
    uvicorn smalldoge_webui.main:app \
        --host "$HOST" \
        --port "$PORT" \
        --reload \
        --log-level debug \
        --access-log
else
    echo "Running in production mode..."
    WORKERS="${UVICORN_WORKERS:-1}"
    echo "Using $WORKERS workers"
    
    uvicorn smalldoge_webui.main:app \
        --host "$HOST" \
        --port "$PORT" \
        --workers "$WORKERS" \
        --log-level info \
        --access-log
fi
