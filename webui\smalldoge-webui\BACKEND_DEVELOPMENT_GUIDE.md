# SmallDoge WebUI 后端开发文档

## 概述

本文档基于 open-webui 的后端架构设计，为 SmallDoge WebUI 项目提供模型推理后端开发指南。后端使用 FastAPI 框架，前端将使用 Gradio 替代 open-webui 的默认前端。推理框架使用transformers.

## 架构设计

### 核心组件

1. **FastAPI 应用主体** (`main.py`)
2. **路由模块** (`routers/`)
3. **数据模型** (`models/`)
4. **配置管理** (`config.py`)
5. **认证系统** (`auth/`)
6. **工具函数** (`utils/`)

### 目录结构

```
smalldoge-webui/
├── backend/
│   ├── smalldoge_webui/
│   │   ├── __init__.py
│   │   ├── main.py              # FastAPI 应用入口
│   │   ├── config.py            # 配置管理
│   │   ├── constants.py         # 常量定义
│   │   ├── env.py              # 环境变量
│   │   ├── models/             # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── users.py        # 用户模型
│   │   │   ├── models.py       # AI模型管理
│   │   │   ├── chats.py        # 对话记录
│   │   │   └── auths.py        # 认证模型
│   │   ├── routers/            # API路由
│   │   │   ├── __init__.py
│   │   │   ├── auth.py         # 认证相关
│   │   │   ├── models.py       # 模型管理
│   │   │   ├── chat.py         # 对话接口
│   │   │   ├── inference.py    # 推理接口
│   │   │   └── users.py        # 用户管理
│   │   ├── utils/              # 工具函数
│   │   │   ├── __init__.py
│   │   │   ├── auth.py         # 认证工具
│   │   │   ├── models.py       # 模型工具
│   │   │   └── inference.py    # 推理工具
│   │   ├── internal/           # 内部模块
│   │   │   ├── __init__.py
│   │   │   └── db.py           # 数据库连接
│   │   └── storage/            # 存储相关
│   ├── requirements.txt        # 依赖包
│   └── start.sh               # 启动脚本
├── frontend/                   # Gradio 前端
└── docker-compose.yml         # Docker 配置
```

## 核心模块设计

### 1. 主应用 (main.py)

```python
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from smalldoge_webui.routers import (
    auth, models, chat, inference, users
)
from smalldoge_webui.config import (
    ENABLE_SIGNUP, WEBUI_NAME, CORS_ALLOW_ORIGIN
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化
    app.state.models = {}
    yield
    # 关闭时清理

app = FastAPI(
    title="SmallDoge WebUI",
    description="AI Model Inference WebUI Backend",
    version="1.0.0",
    lifespan=lifespan
)

# CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ALLOW_ORIGIN,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 路由注册
app.include_router(auth.router, prefix="/api/v1/auth", tags=["auth"])
app.include_router(models.router, prefix="/api/v1/models", tags=["models"])
app.include_router(chat.router, prefix="/api/v1/chat", tags=["chat"])
app.include_router(inference.router, prefix="/api/v1/inference", tags=["inference"])
app.include_router(users.router, prefix="/api/v1/users", tags=["users"])
```

### 2. 配置管理 (config.py)

```python
import os
from typing import Optional
from pydantic import BaseModel

class DatabaseConfig(BaseModel):
    url: str = os.getenv("DATABASE_URL", "sqlite:///./smalldoge.db")
    echo: bool = os.getenv("DB_ECHO", "false").lower() == "true"

class AuthConfig(BaseModel):
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key")
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    enable_signup: bool = os.getenv("ENABLE_SIGNUP", "true").lower() == "true"

class ModelConfig(BaseModel):
    default_model: str = os.getenv("DEFAULT_MODEL", "")
    model_cache_dir: str = os.getenv("MODEL_CACHE_DIR", "./models")
    max_concurrent_requests: int = int(os.getenv("MAX_CONCURRENT_REQUESTS", "10"))

# 全局配置实例
DATABASE_CONFIG = DatabaseConfig()
AUTH_CONFIG = AuthConfig()
MODEL_CONFIG = ModelConfig()

# 其他配置
WEBUI_NAME = os.getenv("WEBUI_NAME", "SmallDoge WebUI")
CORS_ALLOW_ORIGIN = os.getenv("CORS_ALLOW_ORIGIN", "*").split(",")
```

### 3. 数据模型设计

#### 用户模型 (models/users.py)

```python
from sqlalchemy import Column, String, DateTime, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
from pydantic import BaseModel
from datetime import datetime
from typing import Optional

Base = declarative_base()

class User(Base):
    __tablename__ = "users"
    
    id = Column(String, primary_key=True)
    email = Column(String, unique=True, index=True)
    name = Column(String)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    api_key = Column(String, unique=True, nullable=True)

class UserCreate(BaseModel):
    email: str
    name: str
    password: str

class UserResponse(BaseModel):
    id: str
    email: str
    name: str
    is_active: bool
    is_admin: bool
    created_at: datetime
```

#### AI模型管理 (models/models.py)

```python
from sqlalchemy import Column, String, DateTime, Boolean, Text, JSON
from pydantic import BaseModel
from typing import Optional, Dict, Any

class AIModel(Base):
    __tablename__ = "ai_models"
    
    id = Column(String, primary_key=True)
    name = Column(String)
    description = Column(Text)
    model_type = Column(String)  # ollama, openai, huggingface, etc.
    model_path = Column(String)
    config = Column(JSON)
    is_active = Column(Boolean, default=True)
    created_by = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)

class ModelCreate(BaseModel):
    name: str
    description: Optional[str] = None
    model_type: str
    model_path: str
    config: Optional[Dict[str, Any]] = {}

class ModelResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    model_type: str
    is_active: bool
    created_at: datetime
```

### 4. API 路由设计

#### 认证路由 (routers/auth.py)

```python
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session
from smalldoge_webui.models.users import UserCreate, UserResponse
from smalldoge_webui.utils.auth import (
    authenticate_user, create_access_token, get_current_user
)

router = APIRouter()
security = HTTPBearer()

@router.post("/signup", response_model=UserResponse)
async def signup(user_data: UserCreate, db: Session = Depends(get_db)):
    """用户注册"""
    # 实现用户注册逻辑
    pass

@router.post("/signin")
async def signin(email: str, password: str, db: Session = Depends(get_db)):
    """用户登录"""
    user = authenticate_user(db, email, password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials"
        )
    
    access_token = create_access_token(data={"sub": user.email})
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user = Depends(get_current_user)):
    """获取当前用户信息"""
    return current_user
```

#### 模型管理路由 (routers/models.py)

```python
from fastapi import APIRouter, Depends, HTTPException
from typing import List
from smalldoge_webui.models.models import ModelCreate, ModelResponse

router = APIRouter()

@router.get("/", response_model=List[ModelResponse])
async def list_models(current_user = Depends(get_current_user)):
    """获取可用模型列表"""
    # 实现模型列表获取逻辑
    pass

@router.post("/", response_model=ModelResponse)
async def create_model(
    model_data: ModelCreate,
    current_user = Depends(get_current_user)
):
    """创建新模型配置"""
    # 实现模型创建逻辑
    pass

@router.get("/{model_id}", response_model=ModelResponse)
async def get_model(model_id: str, current_user = Depends(get_current_user)):
    """获取特定模型信息"""
    # 实现模型详情获取逻辑
    pass

@router.delete("/{model_id}")
async def delete_model(model_id: str, current_user = Depends(get_current_user)):
    """删除模型配置"""
    # 实现模型删除逻辑
    pass
```

#### 推理接口 (routers/inference.py)

```python
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import json

router = APIRouter()

class ChatMessage(BaseModel):
    role: str  # system, user, assistant
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = None
    stream: Optional[bool] = False

@router.post("/chat/completions")
async def chat_completions(
    request: ChatCompletionRequest,
    current_user = Depends(get_current_user)
):
    """OpenAI 兼容的对话完成接口"""
    if request.stream:
        return StreamingResponse(
            generate_stream_response(request),
            media_type="text/plain"
        )
    else:
        return await generate_response(request)

async def generate_response(request: ChatCompletionRequest):
    """生成非流式响应"""
    # 实现模型推理逻辑
    pass

async def generate_stream_response(request: ChatCompletionRequest):
    """生成流式响应"""
    # 实现流式推理逻辑
    for chunk in model_inference_stream(request):
        yield f"data: {json.dumps(chunk)}\n\n"
    yield "data: [DONE]\n\n"
```

## 开发步骤

### 第一阶段：基础框架搭建

1. 创建项目目录结构
2. 设置 FastAPI 应用和基础配置
3. 实现数据库连接和模型定义
4. 创建基础的认证系统

### 第二阶段：核心功能实现

1. 实现用户管理功能
2. 实现模型管理功能
3. 实现基础的推理接口
4. 添加日志和错误处理

### 第三阶段：高级功能

1. 实现流式推理
2. 添加模型缓存和优化
3. 实现对话历史管理
4. 添加监控和性能指标

### 第四阶段：集成和测试

1. 与 Gradio 前端集成
2. 编写单元测试和集成测试
3. 性能优化和安全加固
4. 部署配置和文档完善

## 技术栈

- **Web框架**: FastAPI
- **数据库**: SQLAlchemy + SQLite/PostgreSQL
- **认证**: JWT + bcrypt
- **异步**: asyncio + aiohttp
- **模型推理**: 支持多种后端 (Ollama, OpenAI API, HuggingFace)
- **前端**: Gradio (替代 open-webui 前端)

## 环境配置

### 环境变量配置

创建 `.env` 文件：

```bash
# 数据库配置
DATABASE_URL=sqlite:///./smalldoge.db
DB_ECHO=false

# 认证配置
SECRET_KEY=your-super-secret-key-here
ENABLE_SIGNUP=true

# 模型配置
DEFAULT_MODEL=llama2
MODEL_CACHE_DIR=./models
MAX_CONCURRENT_REQUESTS=10

# WebUI 配置
WEBUI_NAME=SmallDoge WebUI
CORS_ALLOW_ORIGIN=http://localhost:3000,http://localhost:7860

# 日志配置
LOG_LEVEL=INFO

# 外部服务配置
OLLAMA_BASE_URL=http://localhost:11434
OPENAI_API_KEY=your-openai-api-key
HUGGINGFACE_API_TOKEN=your-hf-token
```

### 依赖包 (requirements.txt)

```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlalchemy==2.0.23
alembic==1.12.1
pydantic==2.5.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
aiofiles==23.2.1
httpx==0.25.2
redis==5.0.1
celery==5.3.4
gradio==4.8.0
transformers==4.36.0
torch==2.1.1
numpy==1.24.3
pandas==2.1.3
```

## 详细实现指南

### 1. 数据库初始化 (internal/db.py)

```python
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from smalldoge_webui.config import DATABASE_CONFIG

engine = create_engine(
    DATABASE_CONFIG.url,
    echo=DATABASE_CONFIG.echo,
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_CONFIG.url else {}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """初始化数据库表"""
    Base.metadata.create_all(bind=engine)
```

### 2. 认证工具 (utils/auth.py)

```python
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from smalldoge_webui.config import AUTH_CONFIG
from smalldoge_webui.internal.db import get_db
from smalldoge_webui.models.users import User

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=AUTH_CONFIG.access_token_expire_minutes)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, AUTH_CONFIG.secret_key, algorithm=AUTH_CONFIG.algorithm)
    return encoded_jwt

def authenticate_user(db: Session, email: str, password: str):
    user = db.query(User).filter(User.email == email).first()
    if not user or not verify_password(password, user.hashed_password):
        return False
    return user

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(
            credentials.credentials,
            AUTH_CONFIG.secret_key,
            algorithms=[AUTH_CONFIG.algorithm]
        )
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user = db.query(User).filter(User.email == email).first()
    if user is None:
        raise credentials_exception
    return user
```

### 3. 模型推理工具 (utils/inference.py)

```python
import asyncio
import httpx
from typing import AsyncGenerator, Dict, Any
from smalldoge_webui.config import MODEL_CONFIG

class ModelInferenceEngine:
    def __init__(self):
        self.ollama_client = httpx.AsyncClient()
        self.openai_client = httpx.AsyncClient()

    async def chat_completion(
        self,
        model: str,
        messages: list,
        stream: bool = False,
        **kwargs
    ):
        """统一的对话完成接口"""
        model_info = await self.get_model_info(model)

        if model_info["type"] == "ollama":
            return await self._ollama_chat(model, messages, stream, **kwargs)
        elif model_info["type"] == "openai":
            return await self._openai_chat(model, messages, stream, **kwargs)
        elif model_info["type"] == "huggingface":
            return await self._hf_chat(model, messages, stream, **kwargs)
        else:
            raise ValueError(f"Unsupported model type: {model_info['type']}")

    async def _ollama_chat(self, model: str, messages: list, stream: bool, **kwargs):
        """Ollama 模型推理"""
        url = f"{os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')}/api/chat"
        payload = {
            "model": model,
            "messages": messages,
            "stream": stream,
            **kwargs
        }

        if stream:
            return self._ollama_stream(url, payload)
        else:
            response = await self.ollama_client.post(url, json=payload)
            return response.json()

    async def _ollama_stream(self, url: str, payload: dict) -> AsyncGenerator[dict, None]:
        """Ollama 流式响应"""
        async with self.ollama_client.stream("POST", url, json=payload) as response:
            async for line in response.aiter_lines():
                if line:
                    try:
                        data = json.loads(line)
                        yield data
                    except json.JSONDecodeError:
                        continue

    async def get_model_info(self, model_id: str) -> Dict[str, Any]:
        """获取模型信息"""
        # 从数据库或配置中获取模型信息
        # 这里简化实现
        return {
            "id": model_id,
            "type": "ollama",  # 默认类型
            "config": {}
        }

# 全局推理引擎实例
inference_engine = ModelInferenceEngine()
```

### 4. 启动脚本 (start.sh)

```bash
#!/bin/bash

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python -c "from smalldoge_webui.internal.db import init_db; init_db()"

# 启动服务
uvicorn smalldoge_webui.main:app --host 0.0.0.0 --port 8000 --reload
```

### 5. Docker 配置 (docker-compose.yml)

```yaml
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/smalldoge
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - ./models:/app/models

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "7860:7860"
    environment:
      - BACKEND_URL=http://backend:8000
    depends_on:
      - backend

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=smalldoge
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

## API 文档示例

### 认证 API

- `POST /api/v1/auth/signup` - 用户注册
- `POST /api/v1/auth/signin` - 用户登录
- `GET /api/v1/auth/me` - 获取当前用户信息
- `POST /api/v1/auth/refresh` - 刷新访问令牌

### 模型管理 API

- `GET /api/v1/models` - 获取模型列表
- `POST /api/v1/models` - 创建模型配置
- `GET /api/v1/models/{model_id}` - 获取模型详情
- `PUT /api/v1/models/{model_id}` - 更新模型配置
- `DELETE /api/v1/models/{model_id}` - 删除模型配置

### 推理 API

- `POST /api/v1/inference/chat/completions` - 对话完成 (OpenAI 兼容)
- `POST /api/v1/inference/embeddings` - 文本嵌入
- `GET /api/v1/inference/models` - 获取可用推理模型

### 对话管理 API

- `GET /api/v1/chats` - 获取对话列表
- `POST /api/v1/chats` - 创建新对话
- `GET /api/v1/chats/{chat_id}` - 获取对话详情
- `PUT /api/v1/chats/{chat_id}` - 更新对话
- `DELETE /api/v1/chats/{chat_id}` - 删除对话

## 测试策略

### 单元测试 (tests/test_auth.py)

```python
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from smalldoge_webui.main import app
from smalldoge_webui.internal.db import get_db, Base

# 测试数据库
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture
def client():
    Base.metadata.create_all(bind=engine)
    with TestClient(app) as c:
        yield c
    Base.metadata.drop_all(bind=engine)

def test_signup(client):
    response = client.post(
        "/api/v1/auth/signup",
        json={
            "email": "<EMAIL>",
            "name": "Test User",
            "password": "testpassword"
        }
    )
    assert response.status_code == 200
    assert response.json()["email"] == "<EMAIL>"

def test_signin(client):
    # 先注册用户
    client.post("/api/v1/auth/signup", json={
        "email": "<EMAIL>",
        "name": "Test User",
        "password": "testpassword"
    })

    # 测试登录
    response = client.post(
        "/api/v1/auth/signin",
        data={"username": "<EMAIL>", "password": "testpassword"}
    )
    assert response.status_code == 200
    assert "access_token" in response.json()
```

### 集成测试 (tests/test_inference.py)

```python
import pytest
from unittest.mock import AsyncMock, patch
from smalldoge_webui.utils.inference import ModelInferenceEngine

@pytest.mark.asyncio
async def test_ollama_chat_completion():
    engine = ModelInferenceEngine()

    with patch.object(engine.ollama_client, 'post') as mock_post:
        mock_response = AsyncMock()
        mock_response.json.return_value = {
            "message": {"content": "Hello, how can I help you?"}
        }
        mock_post.return_value = mock_response

        result = await engine._ollama_chat(
            model="llama2",
            messages=[{"role": "user", "content": "Hello"}],
            stream=False
        )

        assert "message" in result
        assert result["message"]["content"] == "Hello, how can I help you?"
```

### 性能测试 (tests/test_performance.py)

```python
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from fastapi.testclient import TestClient
from smalldoge_webui.main import app

def test_concurrent_requests():
    """测试并发请求性能"""
    client = TestClient(app)

    def make_request():
        return client.get("/api/v1/models")

    start_time = time.time()
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(make_request) for _ in range(100)]
        results = [future.result() for future in futures]

    end_time = time.time()

    # 检查所有请求都成功
    assert all(r.status_code == 200 for r in results)

    # 检查性能要求 (100个请求在5秒内完成)
    assert end_time - start_time < 5.0
```

## 部署指南

### 生产环境配置

#### 1. 环境变量 (.env.production)

```bash
# 生产环境配置
ENV=production
DEBUG=false

# 数据库 (使用 PostgreSQL)
DATABASE_URL=postgresql://user:password@localhost:5432/smalldoge_prod

# 安全配置
SECRET_KEY=your-super-secure-secret-key-for-production
CORS_ALLOW_ORIGIN=https://yourdomain.com

# 性能配置
MAX_CONCURRENT_REQUESTS=50
UVICORN_WORKERS=4

# 日志配置
LOG_LEVEL=WARNING
LOG_FILE=/var/log/smalldoge/app.log

# 外部服务
OLLAMA_BASE_URL=http://ollama-server:11434
REDIS_URL=redis://redis-server:6379
```

#### 2. Nginx 配置 (nginx.conf)

```nginx
upstream smalldoge_backend {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
    server 127.0.0.1:8003;
}

server {
    listen 80;
    server_name yourdomain.com;

    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;

    # API 请求代理到后端
    location /api/ {
        proxy_pass http://smalldoge_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 前端静态文件
    location / {
        proxy_pass http://127.0.0.1:7860;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 3. Systemd 服务配置 (smalldoge-webui.service)

```ini
[Unit]
Description=SmallDoge WebUI Backend
After=network.target postgresql.service redis.service

[Service]
Type=exec
User=smalldoge
Group=smalldoge
WorkingDirectory=/opt/smalldoge-webui/backend
Environment=PATH=/opt/smalldoge-webui/backend/venv/bin
ExecStart=/opt/smalldoge-webui/backend/venv/bin/uvicorn smalldoge_webui.main:app --host 0.0.0.0 --port 8000 --workers 4
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

### 监控和日志

#### 1. 日志配置 (utils/logging.py)

```python
import logging
import logging.handlers
import os
from datetime import datetime

def setup_logging():
    """配置应用日志"""
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    log_file = os.getenv("LOG_FILE", "smalldoge.log")

    # 创建日志目录
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 配置根日志器
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.handlers.RotatingFileHandler(
                log_file, maxBytes=10*1024*1024, backupCount=5
            )
        ]
    )

    # 配置访问日志
    access_logger = logging.getLogger("uvicorn.access")
    access_handler = logging.handlers.RotatingFileHandler(
        f"{log_dir}/access.log", maxBytes=10*1024*1024, backupCount=5
    )
    access_handler.setFormatter(
        logging.Formatter('%(asctime)s - %(message)s')
    )
    access_logger.addHandler(access_handler)
```

#### 2. 健康检查端点

```python
from fastapi import APIRouter
from sqlalchemy import text
from smalldoge_webui.internal.db import get_db

router = APIRouter()

@router.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        db = next(get_db())
        db.execute(text("SELECT 1"))
        db_status = "healthy"
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"

    return {
        "status": "healthy" if db_status == "healthy" else "unhealthy",
        "timestamp": datetime.utcnow().isoformat(),
        "database": db_status,
        "version": "1.0.0"
    }

@router.get("/metrics")
async def metrics():
    """Prometheus 兼容的指标端点"""
    # 实现指标收集逻辑
    return {
        "active_connections": 10,
        "total_requests": 1000,
        "average_response_time": 0.5
    }
```

## 最佳实践

### 1. 安全最佳实践

- 使用强密码哈希 (bcrypt)
- 实施 JWT 令牌过期和刷新机制
- 启用 CORS 保护
- 输入验证和清理
- SQL 注入防护 (使用 ORM)
- 限制 API 请求频率

### 2. 性能优化

- 使用连接池管理数据库连接
- 实施缓存策略 (Redis)
- 异步处理长时间运行的任务
- 模型预加载和缓存
- 请求/响应压缩

### 3. 代码质量

- 遵循 PEP 8 代码风格
- 使用类型提示
- 编写全面的测试
- 代码审查流程
- 自动化 CI/CD

### 4. 错误处理

```python
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)

    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "request_id": getattr(request.state, "request_id", None)
        }
    )

@app.middleware("http")
async def add_request_id(request: Request, call_next):
    """添加请求 ID 中间件"""
    import uuid
    request.state.request_id = str(uuid.uuid4())
    response = await call_next(request)
    response.headers["X-Request-ID"] = request.state.request_id
    return response
```

## 下一步

1. **立即开始**: 创建项目基础结构
2. **第一周**: 实现认证和用户管理
3. **第二周**: 实现模型管理和基础推理
4. **第三周**: 集成 Gradio 前端
5. **第四周**: 测试、优化和部署准备

## 参考资源

- [FastAPI 官方文档](https://fastapi.tiangolo.com/)
- [SQLAlchemy 文档](https://docs.sqlalchemy.org/)
- [Gradio 文档](https://gradio.app/docs/)
- [Open WebUI 源码](https://github.com/open-webui/open-webui)
