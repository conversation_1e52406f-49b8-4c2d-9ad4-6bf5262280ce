#!/usr/bin/env python3
"""
Test script for model creation functionality
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_model_creation():
    """Test model creation with proper data"""
    print("Testing model creation...")
    
    try:
        from smalldoge_webui.models.models import ModelCreate, ModelConfig
        
        # Test creating ModelConfig
        config = ModelConfig(
            max_length=2048,
            temperature=0.7,
            top_p=0.9,
            top_k=50,
            repetition_penalty=1.1,
            do_sample=True,
            load_in_8bit=False,
            load_in_4bit=False,
            device_map="auto",
            torch_dtype="auto",
            trust_remote_code=False
        )
        print("✓ ModelConfig created successfully")
        print(f"  Config: {config}")
        
        # Test creating ModelCreate
        model_create = ModelCreate(
            id="test-model-123",
            name="Test Model",
            description="A test model for validation",
            model_type="transformers",
            model_path="SmallDoge/Doge-160M",
            config=config,
            is_public=True
        )
        print("✓ ModelCreate created successfully")
        print(f"  Model ID: {model_create.id}")
        print(f"  Model Name: {model_create.name}")
        print(f"  Model Type: {model_create.model_type}")
        print(f"  Model Path: {model_create.model_path}")
        print(f"  Has Config: {model_create.config is not None}")
        
        # Test accessing attributes
        print(f"  Accessing id attribute: {getattr(model_create, 'id', 'NOT FOUND')}")
        print(f"  Has id attribute: {hasattr(model_create, 'id')}")
        
        # Test model_dump
        model_dict = model_create.model_dump()
        print("✓ model_dump() works")
        print(f"  Dict keys: {list(model_dict.keys())}")
        print(f"  Dict id: {model_dict.get('id', 'NOT FOUND')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_serialization():
    """Test JSON serialization of model data"""
    print("\nTesting JSON serialization...")
    
    try:
        import json
        
        # Test data similar to what frontend sends
        test_data = {
            "id": "doge-160m",
            "name": "Doge-160M",
            "description": "Doge-160M - transformers model",
            "model_type": "transformers",
            "model_path": "SmallDoge/Doge-160M",
            "config": {
                "max_length": 2048,
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 50,
                "repetition_penalty": 1.1,
                "do_sample": True,
                "load_in_8bit": False,
                "load_in_4bit": False,
                "device_map": "auto",
                "torch_dtype": "auto",
                "trust_remote_code": False
            },
            "is_public": True
        }
        
        # Test JSON serialization
        json_str = json.dumps(test_data)
        print("✓ JSON serialization works")
        
        # Test JSON deserialization
        parsed_data = json.loads(json_str)
        print("✓ JSON deserialization works")
        
        # Test Pydantic validation
        from smalldoge_webui.models.models import ModelCreate
        model_create = ModelCreate(**parsed_data)
        print("✓ Pydantic validation works")
        print(f"  Validated model ID: {model_create.id}")
        
        return True
        
    except Exception as e:
        print(f"✗ JSON serialization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_creation():
    """Test database model creation"""
    print("\nTesting database model creation...")
    
    try:
        from smalldoge_webui.models.models import ModelCreate, Models
        from smalldoge_webui.internal.db import init_db
        
        # Initialize database
        init_db()
        print("✓ Database initialized")
        
        # Create test model data
        model_data = ModelCreate(
            id="test-db-model",
            name="Test DB Model",
            description="A test model for database validation",
            model_type="transformers",
            model_path="test/model",
            is_public=True
        )
        
        # Test model creation in database
        test_user_id = "test-user-123"
        created_model = Models.create_model(model_data, test_user_id)
        
        if created_model:
            print("✓ Database model creation works")
            print(f"  Created model ID: {created_model.id}")
            print(f"  Created model name: {created_model.name}")
            
            # Clean up - delete the test model
            Models.delete_model_by_id("test-db-model")
            print("✓ Test model cleaned up")
        else:
            print("✗ Database model creation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Database model creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("SmallDoge WebUI Model Creation Test")
    print("=" * 50)
    
    tests = [
        test_model_creation,
        test_json_serialization,
        test_database_creation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Model creation is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
