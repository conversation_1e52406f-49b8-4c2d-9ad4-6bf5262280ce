# SmallDoge WebUI Frontend Configuration

# Backend API Configuration
BACKEND_URL=http://localhost:8000
BACKEND_API_BASE=/api/v1

# Gradio Server Configuration
GRADIO_SERVER_NAME=0.0.0.0
GRADIO_SERVER_PORT=7860
GRADIO_SHARE=false
GRADIO_DEBUG=true

# UI Configuration
GRADIO_THEME=soft
GRADIO_TITLE=SmallDoge WebUI
GRADIO_DESCRIPTION=基于 Transformers 的 AI 模型推理平台

# Optional: Authentication
# GRADIO_AUTH_USERNAME=admin
# GRADIO_AUTH_PASSWORD=password

# Optional: SSL Configuration
# GRADIO_SSL_KEYFILE=
# GRADIO_SSL_CERTFILE=

# Development settings
DEV_MODE=true
AUTO_RELOAD=true
