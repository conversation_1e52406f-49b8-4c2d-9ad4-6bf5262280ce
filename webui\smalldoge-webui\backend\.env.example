# SmallDoge WebUI Environment Configuration
# Copy this file to .env and modify the values as needed

# Environment
ENV=dev
DEBUG=true

# Server Configuration
HOST=0.0.0.0
PORT=8000
UVICORN_WORKERS=1

# Security
WEBUI_SECRET_KEY=your-super-secret-key-here
WEBUI_AUTH=true

# Database Configuration
DATABASE_URL=sqlite:///./data/smalldoge.db
# For PostgreSQL: postgresql://user:password@localhost:5432/smalldoge
# For MySQL: mysql://user:password@localhost:3306/smalldoge

# Authentication
ENABLE_SIGNUP=true
ENABLE_LOGIN_FORM=true
DEFAULT_USER_ROLE=user
ACCESS_TOKEN_EXPIRE_MINUTES=1440
REFRESH_TOKEN_EXPIRE_DAYS=7

# Model Configuration
DEFAULT_MODEL=
MODEL_CACHE_DIR=./data/models
MAX_CONCURRENT_REQUESTS=10

# Device Configuration
DEVICE=auto
TORCH_DTYPE=auto
LOAD_IN_8BIT=false
LOAD_IN_4BIT=false
USE_FLASH_ATTENTION=false

# Transformers Configuration
TRANSFORMERS_CACHE_DIR=./data/models/transformers
HUGGINGFACE_HUB_CACHE=./data/models/hub

# External API Configuration
OPENAI_API_KEY=
OPENAI_API_BASE_URL=https://api.openai.com/v1
HUGGINGFACE_API_TOKEN=

# CORS Configuration
CORS_ALLOW_ORIGIN=*

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./data/uploads

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_PER_MINUTE=60

# Logging Configuration
GLOBAL_LOG_LEVEL=INFO
MAIN_LOG_LEVEL=INFO
DB_LOG_LEVEL=INFO
MODELS_LOG_LEVEL=INFO
INFERENCE_LOG_LEVEL=INFO

# Data Directory
DATA_DIR=./data

# WebUI Configuration
WEBUI_NAME=SmallDoge WebUI
WEBUI_URL=

# Features
ENABLE_API_DOCS=true
ENABLE_HEALTH_CHECKS=true
ENABLE_METRICS=true
ENABLE_WEBSOCKET_SUPPORT=true

# Gradio Frontend Configuration
GRADIO_HOST=0.0.0.0
GRADIO_PORT=7860
GRADIO_SHARE=false

# Optional: Redis Configuration
# REDIS_URL=redis://localhost:6379

# Optional: Thread Pool Configuration
THREAD_POOL_SIZE=10

# Development Configuration
RELOAD_ON_CHANGE=true
