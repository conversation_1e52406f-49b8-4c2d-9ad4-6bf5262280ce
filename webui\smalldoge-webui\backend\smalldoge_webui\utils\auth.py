"""
Authentication Utilities for SmallDoge WebUI
Based on open-webui auth utilities with JWT and bcrypt
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Union

from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from smalldoge_webui.config import AUTH_CONFIG
from smalldoge_webui.models.users import Users, UserModel
from smalldoge_webui.models.auths import Auths
from smalldoge_webui.constants import ERROR_MESSAGES
from smalldoge_webui.env import SRC_LOG_LEVELS

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["MAIN"])

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token security
security = HTTPBearer(auto_error=False)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        log.error(f"Error verifying password: {e}")
        return False

def get_password_hash(password: str) -> str:
    """Generate password hash"""
    try:
        return pwd_context.hash(password)
    except Exception as e:
        log.error(f"Error hashing password: {e}")
        raise

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    try:
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=AUTH_CONFIG["access_token_expire_minutes"])
        
        to_encode.update({"exp": expire, "iat": datetime.utcnow()})
        
        from smalldoge_webui.env import WEBUI_SECRET_KEY
        encoded_jwt = jwt.encode(
            to_encode,
            WEBUI_SECRET_KEY,
            algorithm="HS256"
        )
        
        return encoded_jwt
    except Exception as e:
        log.error(f"Error creating access token: {e}")
        raise

def create_refresh_token(data: dict) -> str:
    """Create JWT refresh token"""
    try:
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=AUTH_CONFIG["refresh_token_expire_days"])
        
        to_encode.update({"exp": expire, "iat": datetime.utcnow(), "type": "refresh"})
        
        from smalldoge_webui.env import WEBUI_SECRET_KEY
        encoded_jwt = jwt.encode(
            to_encode,
            WEBUI_SECRET_KEY,
            algorithm="HS256"
        )
        
        return encoded_jwt
    except Exception as e:
        log.error(f"Error creating refresh token: {e}")
        raise

def decode_token(token: str) -> Optional[dict]:
    """Decode JWT token"""
    try:
        from smalldoge_webui.env import WEBUI_SECRET_KEY
        payload = jwt.decode(
            token,
            WEBUI_SECRET_KEY,
            algorithms=["HS256"]
        )
        return payload
    except JWTError as e:
        log.debug(f"JWT decode error: {e}")
        return None
    except Exception as e:
        log.error(f"Error decoding token: {e}")
        return None

def authenticate_user(email: str, password: str) -> Optional[UserModel]:
    """Authenticate user with email and password"""
    try:
        user = Auths.authenticate_user(email, password)
        if user:
            log.info(f"User authenticated: {user.email}")
            return user
        else:
            log.warning(f"Authentication failed for email: {email}")
            return None
    except Exception as e:
        log.error(f"Error authenticating user: {e}")
        return None

def get_current_user_from_token(token: str) -> Optional[UserModel]:
    """Get current user from JWT token"""
    try:
        payload = decode_token(token)
        if not payload:
            return None
        
        # Get user email from token
        user_email: str = payload.get("sub")
        user_id: str = payload.get("user_id")

        if not user_email and not user_id:
            return None

        # Get user from database (try by email first, then by ID)
        if user_email:
            user = Users.get_user_by_email(user_email)
        elif user_id:
            user = Users.get_user_by_id(user_id)
        else:
            user = None
        if not user or not user.is_active:
            return None
        
        return user
    except Exception as e:
        log.error(f"Error getting user from token: {e}")
        return None

async def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> UserModel:
    """Get current authenticated user (FastAPI dependency)"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=ERROR_MESSAGES.INVALID_TOKEN,
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    token = None
    
    # Try to get token from Authorization header
    if credentials:
        token = credentials.credentials
    
    # Try to get token from cookies as fallback
    if not token and hasattr(request, 'cookies'):
        token = request.cookies.get("token")
    
    if not token:
        raise credentials_exception
    
    # Get user from token
    user = get_current_user_from_token(token)
    if not user:
        raise credentials_exception
    
    # Update last active timestamp
    Users.update_user_last_active_by_id(user.id)
    
    return user

async def get_current_active_user(current_user: UserModel = Depends(get_current_user)) -> UserModel:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=ERROR_MESSAGES.UNAUTHORIZED
        )
    return current_user

async def get_admin_user(current_user: UserModel = Depends(get_current_user)) -> UserModel:
    """Get current admin user"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=ERROR_MESSAGES.ACCESS_PROHIBITED
        )
    return current_user

async def get_verified_user(current_user: UserModel = Depends(get_current_user)) -> UserModel:
    """Get current verified user"""
    if not current_user.is_verified and current_user.role != "admin":
        # For now, we'll allow unverified users but log it
        log.warning(f"Unverified user accessing API: {current_user.email}")
    return current_user

def authenticate_api_key(api_key: str) -> Optional[UserModel]:
    """Authenticate user with API key"""
    try:
        if not api_key:
            return None
        
        user = Users.get_user_by_api_key(api_key)
        if user and user.is_active:
            log.info(f"API key authenticated for user: {user.email}")
            Users.update_user_last_active_by_id(user.id)
            return user
        else:
            log.warning(f"Invalid API key used")
            return None
    except Exception as e:
        log.error(f"Error authenticating API key: {e}")
        return None

async def get_current_user_or_api_key(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> UserModel:
    """Get current user from JWT token or API key"""
    # First try JWT token
    try:
        return await get_current_user(request, credentials)
    except HTTPException:
        pass
    
    # Then try API key
    api_key = None
    
    # Check Authorization header for API key
    if credentials and credentials.credentials.startswith("sk-"):
        api_key = credentials.credentials
    
    # Check X-API-Key header
    if not api_key and hasattr(request, 'headers'):
        api_key = request.headers.get("X-API-Key")
    
    if api_key:
        user = authenticate_api_key(api_key)
        if user:
            return user
    
    # If neither works, raise unauthorized
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=ERROR_MESSAGES.INVALID_TOKEN,
        headers={"WWW-Authenticate": "Bearer"},
    )

def validate_password_strength(password: str) -> bool:
    """Validate password strength"""
    if len(password) < 8:
        return False
    
    # Check for at least one uppercase, lowercase, digit
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    
    return has_upper and has_lower and has_digit

def generate_api_key() -> str:
    """Generate a new API key"""
    import secrets
    return f"sk-{secrets.token_urlsafe(32)}"

def create_token_response(user: UserModel) -> dict:
    """Create token response for user"""
    try:
        # Create access token
        access_token = create_access_token(data={"sub": user.id})
        
        # Create refresh token
        refresh_token = create_refresh_token(data={"sub": user.id})
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": AUTH_CONFIG["access_token_expire_minutes"] * 60,
            "user": {
                "id": user.id,
                "name": user.name,
                "email": user.email,
                "role": user.role,
                "is_verified": user.is_verified
            }
        }
    except Exception as e:
        log.error(f"Error creating token response: {e}")
        raise

def refresh_access_token(refresh_token: str) -> Optional[dict]:
    """Refresh access token using refresh token"""
    try:
        payload = decode_token(refresh_token)
        if not payload or payload.get("type") != "refresh":
            return None
        
        user_id = payload.get("sub")
        if not user_id:
            return None
        
        user = Users.get_user_by_id(user_id)
        if not user or not user.is_active:
            return None
        
        # Create new access token
        access_token = create_access_token(data={"sub": user.id})
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": AUTH_CONFIG["access_token_expire_minutes"] * 60
        }
    except Exception as e:
        log.error(f"Error refreshing token: {e}")
        return None

# Export commonly used functions
__all__ = [
    "verify_password", "get_password_hash", "create_access_token", "create_refresh_token",
    "decode_token", "authenticate_user", "get_current_user", "get_current_active_user",
    "get_admin_user", "get_verified_user", "authenticate_api_key", "get_current_user_or_api_key",
    "validate_password_strength", "generate_api_key", "create_token_response", "refresh_access_token"
]
