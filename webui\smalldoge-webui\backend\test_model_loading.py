#!/usr/bin/env python3
"""
Test script for model loading functionality
"""

import sys
import os
import asyncio
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_huggingface_model_access():
    """Test if we can access the HuggingFace model"""
    print("Testing HuggingFace model access...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        model_path = "SmallDoge/Doge-160M"
        print(f"Trying to access model: {model_path}")
        
        # Test tokenizer access
        print("1. Testing tokenizer access...")
        try:
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            print("✓ Tokenizer loaded successfully")
            print(f"  Vocab size: {tokenizer.vocab_size}")
            print(f"  Model max length: {tokenizer.model_max_length}")
        except Exception as e:
            print(f"✗ Tokenizer failed: {e}")
            return False
        
        # Test model access (just check if it exists, don't load)
        print("2. Testing model access...")
        try:
            # Just check if the model config is accessible
            from transformers import AutoConfig
            config = AutoConfig.from_pretrained(model_path)
            print("✓ Model config accessible")
            print(f"  Model type: {config.model_type}")
            print(f"  Hidden size: {config.hidden_size}")
        except Exception as e:
            print(f"✗ Model config failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ HuggingFace model access failed: {e}")
        return False

def test_alternative_models():
    """Test alternative models that are known to work"""
    print("\nTesting alternative models...")
    
    # List of small, reliable models for testing
    test_models = [
        "microsoft/DialoGPT-small",
        "gpt2",
        "distilgpt2"
    ]
    
    for model_name in test_models:
        print(f"\nTesting {model_name}...")
        try:
            from transformers import AutoTokenizer, AutoConfig
            
            # Test tokenizer
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            print(f"✓ {model_name} tokenizer works")
            
            # Test config
            config = AutoConfig.from_pretrained(model_name)
            print(f"✓ {model_name} config works")
            
            return model_name  # Return first working model
            
        except Exception as e:
            print(f"✗ {model_name} failed: {e}")
    
    return None

async def test_inference_engine():
    """Test the inference engine with model loading"""
    print("\nTesting inference engine...")
    
    try:
        from smalldoge_webui.utils.inference import ModelInferenceEngine
        
        engine = ModelInferenceEngine()
        print("✓ Inference engine created")
        print(f"  Device: {engine.device}")
        
        # Test with the original model
        print("\n1. Testing original model (Doge-160M)...")
        model_id = "Doge-160M"
        
        # Check if model exists in database
        from smalldoge_webui.models.models import Models
        model_data = Models.get_model_by_id(model_id)
        
        if model_data:
            print(f"✓ Model found in database: {model_data.model_path}")
            
            # Try to load the model
            success = await engine.load_model(model_id)
            if success:
                print("✓ Model loaded successfully")
                
                # Test model status
                status = await engine.get_model_status(model_id)
                print(f"  Status: {status}")
                
                return True
            else:
                print("✗ Model loading failed")
                return False
        else:
            print("✗ Model not found in database")
            return False
            
    except Exception as e:
        print(f"✗ Inference engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_with_alternative_model():
    """Test with an alternative model"""
    print("\nTesting with alternative model...")
    
    # Find a working alternative model
    working_model = test_alternative_models()
    if not working_model:
        print("✗ No working alternative models found")
        return False
    
    print(f"\nUsing {working_model} for testing...")
    
    try:
        # Create a test model in the database
        from smalldoge_webui.models.models import Models, ModelCreate
        
        test_model_data = ModelCreate(
            id="test-model",
            name="Test Model",
            description="Test model for validation",
            model_type="transformers",
            model_path=working_model,
            is_public=True
        )
        
        # Create model in database
        created_model = Models.create_model(test_model_data, "test-user")
        if created_model:
            print(f"✓ Test model created in database")
            
            # Test with inference engine
            from smalldoge_webui.utils.inference import ModelInferenceEngine
            engine = ModelInferenceEngine()
            
            success = await engine.load_model("test-model")
            if success:
                print("✓ Test model loaded successfully")
                
                # Test chat completion
                messages = [{"role": "user", "content": "Hello"}]
                response = await engine.chat_completion("test-model", messages)
                print(f"✓ Chat completion works: {response.get('content', '')[:50]}...")
                
                # Clean up
                Models.delete_model_by_id("test-model")
                print("✓ Test model cleaned up")
                
                return True
            else:
                print("✗ Test model loading failed")
                Models.delete_model_by_id("test-model")
                return False
        else:
            print("✗ Failed to create test model in database")
            return False
            
    except Exception as e:
        print(f"✗ Alternative model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_solutions():
    """Suggest solutions based on test results"""
    print("\n" + "=" * 50)
    print("SUGGESTED SOLUTIONS:")
    print("=" * 50)
    
    print("\n1. 🔄 Use a different model:")
    print("   - Replace 'SmallDoge/Doge-160M' with a known working model")
    print("   - Suggestions: 'gpt2', 'distilgpt2', 'microsoft/DialoGPT-small'")
    
    print("\n2. 🌐 Check network connection:")
    print("   - Ensure internet access to download from HuggingFace")
    print("   - Check if HuggingFace Hub is accessible")
    
    print("\n3. 📁 Use local model:")
    print("   - Download the model manually and use local path")
    print("   - Update model_path to point to local directory")
    
    print("\n4. 🔧 Update model configuration:")
    print("   - Check if 'SmallDoge/Doge-160M' actually exists on HuggingFace")
    print("   - Update to correct model name if needed")

async def main():
    """Run all tests"""
    print("SmallDoge WebUI Model Loading Test")
    print("=" * 50)
    
    # Test 1: HuggingFace model access
    hf_success = test_huggingface_model_access()
    
    # Test 2: Alternative models
    alt_success = test_alternative_models() is not None
    
    # Test 3: Inference engine
    engine_success = await test_inference_engine()
    
    # Test 4: Alternative model with inference engine
    if not engine_success and alt_success:
        alt_engine_success = await test_with_alternative_model()
    else:
        alt_engine_success = False
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST RESULTS:")
    print("=" * 50)
    print(f"HuggingFace model access: {'✓' if hf_success else '✗'}")
    print(f"Alternative models: {'✓' if alt_success else '✗'}")
    print(f"Inference engine: {'✓' if engine_success else '✗'}")
    print(f"Alternative with engine: {'✓' if alt_engine_success else '✗'}")
    
    if not any([hf_success, engine_success, alt_engine_success]):
        suggest_solutions()
        return False
    
    if engine_success:
        print("\n🎉 Original model works! The issue might be temporary.")
    elif alt_engine_success:
        print("\n💡 Alternative model works! Consider switching to a different model.")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
