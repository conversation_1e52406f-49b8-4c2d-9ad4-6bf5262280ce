@echo off
REM SmallDoge WebUI Complete Startup Script
REM Starts both backend and frontend services

echo ========================================
echo SmallDoge WebUI Complete Startup
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to your PATH
    pause
    exit /b 1
)

echo Python version:
python --version
echo.

REM Create main data directory
if not exist "data" mkdir data

REM Function to start backend
echo [1/3] Setting up backend...
cd backend

REM Check if backend virtual environment exists
if not exist "venv" (
    echo Creating backend virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Error: Failed to create backend virtual environment
        pause
        exit /b 1
    )
)

REM Activate backend virtual environment and install dependencies
echo Installing backend dependencies...
call venv\Scripts\activate.bat
pip install --upgrade pip >nul 2>&1
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo Error: Failed to install backend dependencies
    pause
    exit /b 1
)

REM Initialize backend database
echo Initializing database...
python -c "from smalldoge_webui.internal.db import init_db; init_db()" >nul 2>&1

echo ✓ Backend setup completed
cd ..

REM Function to start frontend
echo [2/3] Setting up frontend...
cd frontend

REM Check if frontend virtual environment exists
if not exist "venv" (
    echo Creating frontend virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Error: Failed to create frontend virtual environment
        pause
        exit /b 1
    )
)

REM Activate frontend virtual environment and install dependencies
echo Installing frontend dependencies...
call venv\Scripts\activate.bat
pip install --upgrade pip >nul 2>&1
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo Error: Failed to install frontend dependencies
    pause
    exit /b 1
)

echo ✓ Frontend setup completed
cd ..

REM Start services
echo [3/3] Starting services...
echo.

REM Create batch files for starting services
echo @echo off > start_backend_service.bat
echo cd backend >> start_backend_service.bat
echo call venv\Scripts\activate.bat >> start_backend_service.bat
echo echo Starting SmallDoge WebUI Backend... >> start_backend_service.bat
echo echo Backend will be available at: http://localhost:8000 >> start_backend_service.bat
echo echo API documentation: http://localhost:8000/docs >> start_backend_service.bat
echo echo. >> start_backend_service.bat
echo python -m smalldoge_webui.main >> start_backend_service.bat

echo @echo off > start_frontend_service.bat
echo cd frontend >> start_frontend_service.bat
echo call venv\Scripts\activate.bat >> start_frontend_service.bat
echo echo Starting SmallDoge WebUI Frontend... >> start_frontend_service.bat
echo echo Frontend will be available at: http://localhost:7860 >> start_frontend_service.bat
echo echo. >> start_frontend_service.bat
echo timeout /t 5 /nobreak ^>nul >> start_frontend_service.bat
echo python app.py >> start_frontend_service.bat

REM Start backend in new window
echo Starting backend service...
start "SmallDoge WebUI Backend" start_backend_service.bat

REM Wait a moment for backend to start
echo Waiting for backend to initialize...
timeout /t 10 /nobreak >nul

REM Start frontend in new window
echo Starting frontend service...
start "SmallDoge WebUI Frontend" start_frontend_service.bat

echo.
echo ========================================
echo    SmallDoge WebUI Started Successfully!
echo ========================================
echo.
echo Services:
echo   Backend:  http://localhost:8000
echo   Frontend: http://localhost:7860
echo   API Docs: http://localhost:8000/docs
echo.
echo The services are running in separate windows.
echo Close those windows to stop the services.
echo.
echo First time setup:
echo 1. Wait for both services to start (about 30 seconds)
echo 2. Open http://localhost:7860 in your browser
echo 3. Register a new account
echo 4. Add some models in the "模型管理" tab
echo 5. Start chatting!
echo.
echo Troubleshooting:
echo - If backend fails: Check backend window for errors
echo - If frontend fails: Check frontend window for errors
echo - If connection fails: Ensure both services are running
echo.

REM Wait for user input before closing
echo Press any key to open the frontend in your browser...
pause >nul

REM Open frontend in default browser
start http://localhost:7860

echo.
echo You can now close this window.
echo The services will continue running in their own windows.

REM Clean up temporary batch files after a delay
timeout /t 5 /nobreak >nul
del start_backend_service.bat >nul 2>&1
del start_frontend_service.bat >nul 2>&1
