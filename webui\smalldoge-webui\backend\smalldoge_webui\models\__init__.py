"""
Data Models for SmallDoge WebUI
Based on open-webui model structure
"""

from smalldoge_webui.models.users import User, UserModel, UserCreate, UserUpdate, UserResponse
from smalldoge_webui.models.auths import Auth, AuthModel, SigninForm, SignupForm
from smalldoge_webui.models.models import AIModel, ModelModel, ModelCreate, ModelUpdate, ModelResponse
from smalldoge_webui.models.chats import Chat, ChatModel, ChatCreate, ChatUpdate, ChatResponse

__all__ = [
    # User models
    "User", "UserModel", "UserCreate", "UserUpdate", "UserResponse",
    # Auth models  
    "Auth", "AuthModel", "SigninForm", "SignupForm",
    # AI Model models
    "AIModel", "ModelModel", "ModelCreate", "ModelUpdate", "ModelResponse",
    # Chat models
    "Chat", "ChatModel", "ChatCreate", "ChatUpdate", "ChatResponse",
]
