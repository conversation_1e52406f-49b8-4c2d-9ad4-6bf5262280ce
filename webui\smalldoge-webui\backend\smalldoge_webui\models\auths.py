"""
Authentication Models for SmallDoge WebUI
Based on open-webui auth model structure
"""

import time
import uuid
from typing import Optional
from datetime import datetime

from sqlalchemy import Column, String, BigInteger, Boolean, Text
from pydantic import BaseModel, EmailStr, Field, ConfigDict

from smalldoge_webui.internal.db import Base, get_db
from smalldoge_webui.models.users import Users, UserModel, UserCreate

# SQLAlchemy Auth Model
class Auth(Base):
    __tablename__ = "auths"

    id = Column(String, primary_key=True)  # Same as user ID
    email = Column(String, unique=True, nullable=False, index=True)
    password = Column(String, nullable=False)  # Hashed password
    active = Column(Boolean, default=True)
    created_at = Column(BigInteger, default=lambda: int(time.time()))
    updated_at = Column(BigInteger, default=lambda: int(time.time()))

# Pydantic Models for API

class AuthModel(BaseModel):
    """Auth model for API responses"""
    id: str
    email: str
    active: bool
    created_at: int
    updated_at: int

    model_config = ConfigDict(from_attributes=True)

class SigninForm(BaseModel):
    """User signin form"""
    email: EmailStr
    password: str = Field(..., min_length=1)

class SignupForm(BaseModel):
    """User signup form"""
    name: str = Field(..., min_length=1, max_length=100)
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=128)

class TokenResponse(BaseModel):
    """Token response model"""
    access_token: str
    token_type: str = "bearer"
    expires_in: Optional[int] = None

class PasswordChangeForm(BaseModel):
    """Password change form"""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=128)

class PasswordResetForm(BaseModel):
    """Password reset form"""
    email: EmailStr

class PasswordResetConfirmForm(BaseModel):
    """Password reset confirmation form"""
    token: str
    new_password: str = Field(..., min_length=8, max_length=128)

# Database Operations Class
class Auths:
    """Auth database operations"""
    
    @staticmethod
    def get_auth_by_email(email: str) -> Optional[AuthModel]:
        """Get auth record by email"""
        try:
            with get_db() as db:
                auth = db.query(Auth).filter(Auth.email == email).first()
                return AuthModel.model_validate(auth) if auth else None
        except Exception as e:
            print(f"Error getting auth by email: {e}")
            return None
    
    @staticmethod
    def get_auth_by_id(auth_id: str) -> Optional[AuthModel]:
        """Get auth record by ID"""
        try:
            with get_db() as db:
                auth = db.query(Auth).filter(Auth.id == auth_id).first()
                return AuthModel.model_validate(auth) if auth else None
        except Exception as e:
            print(f"Error getting auth by ID: {e}")
            return None
    
    @staticmethod
    def create_auth(user_id: str, email: str, hashed_password: str) -> Optional[AuthModel]:
        """Create new auth record"""
        try:
            with get_db() as db:
                # Check if auth already exists
                existing_auth = db.query(Auth).filter(Auth.email == email).first()
                if existing_auth:
                    return None
                
                # Create new auth record
                auth = Auth(
                    id=user_id,
                    email=email,
                    password=hashed_password,
                )
                
                db.add(auth)
                db.commit()
                db.refresh(auth)
                
                return AuthModel.model_validate(auth)
        except Exception as e:
            print(f"Error creating auth: {e}")
            return None
    
    @staticmethod
    def update_password_by_id(auth_id: str, new_hashed_password: str) -> bool:
        """Update password by auth ID"""
        try:
            with get_db() as db:
                auth = db.query(Auth).filter(Auth.id == auth_id).first()
                if not auth:
                    return False
                
                auth.password = new_hashed_password
                auth.updated_at = int(time.time())
                
                db.commit()
                return True
        except Exception as e:
            print(f"Error updating password: {e}")
            return False
    
    @staticmethod
    def update_email_by_id(auth_id: str, new_email: str) -> bool:
        """Update email by auth ID"""
        try:
            with get_db() as db:
                # Check if new email already exists
                existing_auth = db.query(Auth).filter(Auth.email == new_email).first()
                if existing_auth and existing_auth.id != auth_id:
                    return False
                
                auth = db.query(Auth).filter(Auth.id == auth_id).first()
                if not auth:
                    return False
                
                auth.email = new_email
                auth.updated_at = int(time.time())
                
                db.commit()
                return True
        except Exception as e:
            print(f"Error updating email: {e}")
            return False
    
    @staticmethod
    def delete_auth_by_id(auth_id: str) -> bool:
        """Delete auth record by ID"""
        try:
            with get_db() as db:
                auth = db.query(Auth).filter(Auth.id == auth_id).first()
                if not auth:
                    return False
                
                db.delete(auth)
                db.commit()
                return True
        except Exception as e:
            print(f"Error deleting auth: {e}")
            return False
    
    @staticmethod
    def deactivate_auth_by_id(auth_id: str) -> bool:
        """Deactivate auth record by ID"""
        try:
            with get_db() as db:
                auth = db.query(Auth).filter(Auth.id == auth_id).first()
                if not auth:
                    return False
                
                auth.active = False
                auth.updated_at = int(time.time())
                
                db.commit()
                return True
        except Exception as e:
            print(f"Error deactivating auth: {e}")
            return False
    
    @staticmethod
    def activate_auth_by_id(auth_id: str) -> bool:
        """Activate auth record by ID"""
        try:
            with get_db() as db:
                auth = db.query(Auth).filter(Auth.id == auth_id).first()
                if not auth:
                    return False
                
                auth.active = True
                auth.updated_at = int(time.time())
                
                db.commit()
                return True
        except Exception as e:
            print(f"Error activating auth: {e}")
            return False
    
    @staticmethod
    def authenticate_user(email: str, password: str) -> Optional[UserModel]:
        """Authenticate user with email and password"""
        from smalldoge_webui.utils.auth import verify_password
        
        try:
            # Get auth record
            auth = Auths.get_auth_by_email(email)
            if not auth or not auth.active:
                return None
            
            # Verify password
            with get_db() as db:
                auth_record = db.query(Auth).filter(Auth.email == email).first()
                if not auth_record or not verify_password(password, auth_record.password):
                    return None
            
            # Get user record
            user = Users.get_user_by_id(auth.id)
            if not user or not user.is_active:
                return None
            
            # Update last active
            Users.update_user_last_active_by_id(user.id)
            
            return user
            
        except Exception as e:
            print(f"Error authenticating user: {e}")
            return None
    
    @staticmethod
    def create_user_with_auth(signup_data: SignupForm) -> Optional[UserModel]:
        """Create user with auth record"""
        from smalldoge_webui.utils.auth import get_password_hash
        
        try:
            # Check if user already exists
            existing_user = Users.get_user_by_email(signup_data.email)
            if existing_user:
                return None
            
            # Hash password
            hashed_password = get_password_hash(signup_data.password)
            
            # Create user
            user_create = UserCreate(
                name=signup_data.name,
                email=signup_data.email,
                password=signup_data.password  # This won't be stored in user table
            )
            
            user = Users.create_user(user_create, hashed_password)
            if not user:
                return None
            
            # Create auth record
            auth = Auths.create_auth(user.id, signup_data.email, hashed_password)
            if not auth:
                # Rollback user creation
                Users.delete_user_by_id(user.id)
                return None
            
            return user
            
        except Exception as e:
            print(f"Error creating user with auth: {e}")
            return None
    
    @staticmethod
    def get_auth_count() -> int:
        """Get total number of auth records"""
        try:
            with get_db() as db:
                return db.query(Auth).count()
        except Exception as e:
            print(f"Error getting auth count: {e}")
            return 0
