"""
AI Model Management Models for SmallDoge WebUI
Based on open-webui model structure with transformers support
"""

import time
import uuid
from typing import Optional, Dict, Any, List
from datetime import datetime

from sqlalchemy import Column, String, BigInteger, Boolean, Text
from pydantic import BaseModel, Field, ConfigDict

from smalldoge_webui.internal.db import Base, get_db
from smalldoge_webui.models.users import J<PERSON><PERSON><PERSON>
from smalldoge_webui.constants import MODEL_TYPES, DEFAULT_MODEL_CONFIG

# SQLAlchemy AI Model
class AIModel(Base):
    __tablename__ = "ai_models"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    model_type = Column(String, nullable=False)  # transformers, openai, ollama, etc.
    model_path = Column(String, nullable=False)  # HF model ID or local path
    
    # Model configuration as JSON
    config = Column(J<PERSON><PERSON>ield, nullable=True)
    
    # Model metadata
    architecture = Column(String, nullable=True)  # Model architecture type
    parameters = Column(BigInteger, nullable=True)  # Number of parameters
    size_gb = Column(String, nullable=True)  # Model size in GB
    
    # Access control
    is_active = Column(Boolean, default=True)
    is_public = Column(Boolean, default=False)
    created_by = Column(String, nullable=False)  # User ID who created the model
    
    # Timestamps
    created_at = Column(BigInteger, default=lambda: int(time.time()))
    updated_at = Column(BigInteger, default=lambda: int(time.time()))
    last_used_at = Column(BigInteger, nullable=True)
    
    # Usage statistics
    usage_count = Column(BigInteger, default=0)

# Pydantic Models for API

class ModelConfig(BaseModel):
    """Model configuration"""
    max_length: Optional[int] = DEFAULT_MODEL_CONFIG["max_length"]
    temperature: Optional[float] = DEFAULT_MODEL_CONFIG["temperature"]
    top_p: Optional[float] = DEFAULT_MODEL_CONFIG["top_p"]
    top_k: Optional[int] = DEFAULT_MODEL_CONFIG["top_k"]
    repetition_penalty: Optional[float] = DEFAULT_MODEL_CONFIG["repetition_penalty"]
    do_sample: Optional[bool] = DEFAULT_MODEL_CONFIG["do_sample"]
    pad_token_id: Optional[int] = DEFAULT_MODEL_CONFIG["pad_token_id"]
    eos_token_id: Optional[int] = DEFAULT_MODEL_CONFIG["eos_token_id"]
    
    # Transformers specific
    load_in_8bit: Optional[bool] = False
    load_in_4bit: Optional[bool] = False
    device_map: Optional[str] = "auto"
    torch_dtype: Optional[str] = "auto"
    trust_remote_code: Optional[bool] = False
    
    # Additional config
    custom_config: Optional[Dict[str, Any]] = {}
    
    model_config = ConfigDict(extra="allow")

class ModelModel(BaseModel):
    """Base model for API responses"""
    id: str
    name: str
    description: Optional[str] = None
    model_type: str
    model_path: str
    config: Optional[ModelConfig] = None
    architecture: Optional[str] = None
    parameters: Optional[int] = None
    size_gb: Optional[str] = None
    is_active: bool = True
    is_public: bool = False
    created_by: str
    created_at: int
    updated_at: int
    last_used_at: Optional[int] = None
    usage_count: int = 0

    model_config = ConfigDict(from_attributes=True)

class ModelCreate(BaseModel):
    """Model creation"""
    id: str = Field(..., min_length=1, max_length=100, description="Unique model identifier")
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    model_type: str = Field(..., description="Model type (transformers, openai, ollama)")
    model_path: str = Field(..., description="HuggingFace model ID or local path")
    config: Optional[ModelConfig] = None
    architecture: Optional[str] = None
    parameters: Optional[int] = None
    size_gb: Optional[str] = None
    is_public: Optional[bool] = False

class ModelUpdate(BaseModel):
    """Model update"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    config: Optional[ModelConfig] = None
    is_active: Optional[bool] = None
    is_public: Optional[bool] = None

class ModelResponse(BaseModel):
    """Model response (without sensitive data)"""
    id: str
    name: str
    description: Optional[str] = None
    model_type: str
    architecture: Optional[str] = None
    parameters: Optional[int] = None
    size_gb: Optional[str] = None
    is_active: bool
    is_public: bool
    created_at: int
    last_used_at: Optional[int] = None
    usage_count: int

    model_config = ConfigDict(from_attributes=True)

class ModelListResponse(BaseModel):
    """Model list response"""
    models: List[ModelResponse]
    total: int
    page: int
    per_page: int

# Database Operations Class
class Models:
    """Model database operations"""
    
    @staticmethod
    def get_model_by_id(model_id: str) -> Optional[ModelModel]:
        """Get model by ID"""
        try:
            with get_db() as db:
                model = db.query(AIModel).filter(AIModel.id == model_id).first()
                return ModelModel.model_validate(model) if model else None
        except Exception as e:
            print(f"Error getting model by ID: {e}")
            return None
    
    @staticmethod
    def get_model_by_name(name: str) -> Optional[ModelModel]:
        """Get model by name"""
        try:
            with get_db() as db:
                model = db.query(AIModel).filter(AIModel.name == name).first()
                return ModelModel.model_validate(model) if model else None
        except Exception as e:
            print(f"Error getting model by name: {e}")
            return None
    
    @staticmethod
    def create_model(model_data: ModelCreate, user_id: str) -> Optional[ModelModel]:
        """Create new model"""
        try:
            with get_db() as db:
                # Check if model ID already exists
                existing_model = db.query(AIModel).filter(AIModel.id == model_data.id).first()
                if existing_model:
                    return None
                
                # Create new model
                model = AIModel(
                    id=model_data.id,
                    name=model_data.name,
                    description=model_data.description,
                    model_type=model_data.model_type,
                    model_path=model_data.model_path,
                    config=model_data.config.model_dump() if model_data.config else None,
                    architecture=model_data.architecture,
                    parameters=model_data.parameters,
                    size_gb=model_data.size_gb,
                    is_public=model_data.is_public,
                    created_by=user_id,
                )
                
                db.add(model)
                db.commit()
                db.refresh(model)
                
                return ModelModel.model_validate(model)
        except Exception as e:
            print(f"Error creating model: {e}")
            return None
    
    @staticmethod
    def update_model_by_id(model_id: str, model_data: ModelUpdate) -> Optional[ModelModel]:
        """Update model by ID"""
        try:
            with get_db() as db:
                model = db.query(AIModel).filter(AIModel.id == model_id).first()
                if not model:
                    return None
                
                # Update fields
                update_data = model_data.model_dump(exclude_unset=True)
                for field, value in update_data.items():
                    if hasattr(model, field):
                        if field == "config" and value:
                            setattr(model, field, value.model_dump() if hasattr(value, 'model_dump') else value)
                        else:
                            setattr(model, field, value)
                
                model.updated_at = int(time.time())
                
                db.commit()
                db.refresh(model)
                
                return ModelModel.model_validate(model)
        except Exception as e:
            print(f"Error updating model: {e}")
            return None
    
    @staticmethod
    def delete_model_by_id(model_id: str) -> bool:
        """Delete model by ID"""
        try:
            with get_db() as db:
                model = db.query(AIModel).filter(AIModel.id == model_id).first()
                if not model:
                    return False
                
                db.delete(model)
                db.commit()
                return True
        except Exception as e:
            print(f"Error deleting model: {e}")
            return False
    
    @staticmethod
    def get_models(skip: int = 0, limit: int = 100, user_id: Optional[str] = None, 
                   model_type: Optional[str] = None, is_active: Optional[bool] = None) -> List[ModelModel]:
        """Get list of models with filters"""
        try:
            with get_db() as db:
                query = db.query(AIModel)
                
                # Apply filters
                if user_id:
                    query = query.filter(AIModel.created_by == user_id)
                if model_type:
                    query = query.filter(AIModel.model_type == model_type)
                if is_active is not None:
                    query = query.filter(AIModel.is_active == is_active)
                
                models = query.offset(skip).limit(limit).all()
                return [ModelModel.model_validate(model) for model in models]
        except Exception as e:
            print(f"Error getting models: {e}")
            return []
    
    @staticmethod
    def get_public_models(skip: int = 0, limit: int = 100) -> List[ModelModel]:
        """Get list of public models"""
        try:
            with get_db() as db:
                models = db.query(AIModel).filter(
                    AIModel.is_public == True,
                    AIModel.is_active == True
                ).offset(skip).limit(limit).all()
                return [ModelModel.model_validate(model) for model in models]
        except Exception as e:
            print(f"Error getting public models: {e}")
            return []
    
    @staticmethod
    def get_models_count(user_id: Optional[str] = None, model_type: Optional[str] = None) -> int:
        """Get total number of models"""
        try:
            with get_db() as db:
                query = db.query(AIModel)
                
                if user_id:
                    query = query.filter(AIModel.created_by == user_id)
                if model_type:
                    query = query.filter(AIModel.model_type == model_type)
                
                return query.count()
        except Exception as e:
            print(f"Error getting models count: {e}")
            return 0
    
    @staticmethod
    def update_model_usage(model_id: str) -> bool:
        """Update model usage statistics"""
        try:
            with get_db() as db:
                model = db.query(AIModel).filter(AIModel.id == model_id).first()
                if not model:
                    return False
                
                model.usage_count += 1
                model.last_used_at = int(time.time())
                model.updated_at = int(time.time())
                
                db.commit()
                return True
        except Exception as e:
            print(f"Error updating model usage: {e}")
            return False
    
    @staticmethod
    def toggle_model_status(model_id: str) -> Optional[ModelModel]:
        """Toggle model active status"""
        try:
            with get_db() as db:
                model = db.query(AIModel).filter(AIModel.id == model_id).first()
                if not model:
                    return None
                
                model.is_active = not model.is_active
                model.updated_at = int(time.time())
                
                db.commit()
                db.refresh(model)
                
                return ModelModel.model_validate(model)
        except Exception as e:
            print(f"Error toggling model status: {e}")
            return None
