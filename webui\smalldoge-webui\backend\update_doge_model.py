#!/usr/bin/env python3
"""
Script to update Doge-160M model configuration with trust_remote_code=True
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def update_doge_model():
    """Update Doge-160M model configuration"""
    print("Updating Doge-160M model configuration...")
    
    try:
        from smalldoge_webui.models.models import Models, ModelConfig
        from smalldoge_webui.internal.db import init_db
        
        # Initialize database
        init_db()
        print("✓ Database initialized")
        
        # Get existing model
        model_data = Models.get_model_by_id("Doge-160M")
        if not model_data:
            print("✗ Doge-160M model not found in database")
            return False
        
        print(f"✓ Found model: {model_data.name}")
        print(f"  Current path: {model_data.model_path}")
        print(f"  Current config: {model_data.config}")
        
        # Create updated config with trust_remote_code=True
        updated_config = ModelConfig(
            max_length=2048,
            temperature=0.7,
            top_p=0.9,
            top_k=50,
            repetition_penalty=1.1,
            do_sample=True,
            load_in_8bit=False,
            load_in_4bit=False,
            device_map="auto",
            torch_dtype="auto",
            trust_remote_code=True  # This is the key fix!
        )
        
        # Create ModelUpdate object
        from smalldoge_webui.models.models import ModelUpdate

        model_update = ModelUpdate(
            config=updated_config,
            description="Doge-160M - transformers model (with trust_remote_code)"
        )

        # Update the model
        success = Models.update_model_by_id("Doge-160M", model_update)
        
        if success:
            print("✓ Model configuration updated successfully")
            print("  Added trust_remote_code=True")
            
            # Verify the update
            updated_model = Models.get_model_by_id("Doge-160M")
            if updated_model and updated_model.config:
                config_dict = updated_model.config.model_dump() if hasattr(updated_model.config, 'model_dump') else updated_model.config
                print(f"  Updated config: {config_dict}")
                
                if config_dict.get('trust_remote_code'):
                    print("✓ trust_remote_code is now enabled")
                    return True
                else:
                    print("✗ trust_remote_code not found in updated config")
                    return False
            else:
                print("✗ Could not verify updated configuration")
                return False
        else:
            print("✗ Failed to update model configuration")
            return False
            
    except Exception as e:
        print(f"✗ Error updating model: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_loading():
    """Test if the updated model can now be loaded"""
    print("\nTesting updated model loading...")
    
    try:
        import asyncio
        from smalldoge_webui.utils.inference import ModelInferenceEngine
        
        async def test_load():
            engine = ModelInferenceEngine()
            print("✓ Inference engine created")
            
            # Try to load the updated model
            success = await engine.load_model("Doge-160M")
            if success:
                print("✓ Doge-160M model loaded successfully!")
                
                # Test model status
                status = await engine.get_model_status("Doge-160M")
                print(f"  Status: {status}")
                
                # Test a simple chat completion
                messages = [{"role": "user", "content": "Hello"}]
                try:
                    response = await engine.chat_completion("Doge-160M", messages)
                    print(f"✓ Chat completion works: {response.get('content', '')[:100]}...")
                    return True
                except Exception as e:
                    print(f"✗ Chat completion failed: {e}")
                    return False
            else:
                print("✗ Model loading still failed")
                return False
        
        return asyncio.run(test_load())
        
    except Exception as e:
        print(f"✗ Error testing model loading: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_alternative_models():
    """Create alternative working models"""
    print("\nCreating alternative working models...")
    
    try:
        from smalldoge_webui.models.models import Models, ModelCreate
        
        # Create DialoGPT-small model
        dialogpt_model = ModelCreate(
            id="dialogpt-small",
            name="DialoGPT Small",
            description="Microsoft DialoGPT Small - Conversational AI",
            model_type="transformers",
            model_path="microsoft/DialoGPT-small",
            is_public=True
        )
        
        # Check if it already exists
        existing = Models.get_model_by_id("dialogpt-small")
        if not existing:
            created = Models.create_model(dialogpt_model, "system")
            if created:
                print("✓ DialoGPT-small model created")
            else:
                print("✗ Failed to create DialoGPT-small model")
        else:
            print("✓ DialoGPT-small model already exists")
        
        # Create GPT-2 model
        gpt2_model = ModelCreate(
            id="gpt2",
            name="GPT-2",
            description="OpenAI GPT-2 - Base model",
            model_type="transformers",
            model_path="gpt2",
            is_public=True
        )
        
        existing = Models.get_model_by_id("gpt2")
        if not existing:
            created = Models.create_model(gpt2_model, "system")
            if created:
                print("✓ GPT-2 model created")
            else:
                print("✗ Failed to create GPT-2 model")
        else:
            print("✓ GPT-2 model already exists")
        
        return True
        
    except Exception as e:
        print(f"✗ Error creating alternative models: {e}")
        return False

def list_all_models():
    """List all models in the database"""
    print("\nListing all models in database...")
    
    try:
        from smalldoge_webui.models.models import Models
        
        models = Models.get_models()
        if models:
            print(f"Found {len(models)} models:")
            for model in models:
                config_info = ""
                if model.config:
                    config_dict = model.config.model_dump() if hasattr(model.config, 'model_dump') else model.config
                    trust_remote = config_dict.get('trust_remote_code', False)
                    config_info = f" (trust_remote_code={trust_remote})"
                
                print(f"  - {model.id}: {model.name}{config_info}")
                print(f"    Path: {model.model_path}")
        else:
            print("No models found")
        
        return True
        
    except Exception as e:
        print(f"✗ Error listing models: {e}")
        return False

def main():
    """Main function"""
    print("SmallDoge WebUI - Update Doge Model Configuration")
    print("=" * 60)
    
    # Update Doge model configuration
    update_success = update_doge_model()
    
    # Create alternative models
    alt_success = create_alternative_models()
    
    # List all models
    list_all_models()
    
    # Test model loading if update was successful
    if update_success:
        test_success = test_model_loading()
    else:
        test_success = False
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"  Doge model update: {'✓' if update_success else '✗'}")
    print(f"  Alternative models: {'✓' if alt_success else '✗'}")
    print(f"  Model loading test: {'✓' if test_success else '✗'}")
    
    if update_success and test_success:
        print("\n🎉 Doge-160M model is now working!")
        print("You can use it in the frontend for chat.")
    elif alt_success:
        print("\n💡 Alternative models are available:")
        print("  - dialogpt-small (DialoGPT Small)")
        print("  - gpt2 (GPT-2)")
        print("You can use these models while troubleshooting Doge-160M.")
    else:
        print("\n❌ Some issues remain. Check the error messages above.")
    
    return update_success or alt_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
