#!/usr/bin/env python3
"""
Test script for chat API functionality
"""

import sys
import os
import requests
import json
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Backend API configuration
BACKEND_URL = "http://localhost:8000"
API_BASE = f"{BACKEND_URL}/api/v1"

def get_auth_token():
    """Get authentication token"""
    try:
        # Try to sign in first
        signin_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = requests.post(f"{API_BASE}/auth/signin", json=signin_data)
        if response.status_code == 200:
            data = response.json()
            return data.get("token")
        
        # If signin fails, try signup
        signup_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = requests.post(f"{API_BASE}/auth/signup", json=signup_data)
        if response.status_code == 200:
            data = response.json()
            return data.get("token")
        
        return None
        
    except Exception as e:
        print(f"Error getting auth token: {e}")
        return None

def test_pydantic_validation():
    """Test Pydantic model validation directly"""
    print("Testing Pydantic model validation...")
    
    try:
        from smalldoge_webui.routers.inference import ChatCompletionRequest, ChatMessage
        
        # Test valid data
        valid_data = {
            "model": "test-model",
            "messages": [
                {"role": "user", "content": "Hello"}
            ],
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        request = ChatCompletionRequest(**valid_data)
        print("✓ Valid ChatCompletionRequest created")
        print(f"  Model: {request.model}")
        print(f"  Messages: {request.messages}")
        print(f"  Stream: {request.stream}")
        
        # Test message validation
        message = ChatMessage(role="user", content="Hello")
        print("✓ Valid ChatMessage created")
        print(f"  Role: {message.role}")
        print(f"  Content: {message.content}")
        
        return True
        
    except Exception as e:
        print(f"✗ Pydantic validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chat_completion_validation():
    """Test chat completion request validation"""
    print("\nTesting chat completion request validation...")
    
    token = get_auth_token()
    if not token:
        print("✗ Could not get authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test 1: Exact frontend request format
    print("\n1. Testing exact frontend request format...")
    frontend_request = {
        "model": "Doge-160M",
        "messages": [
            {"role": "user", "content": "你好"}
        ],
        "stream": True,
        "temperature": 0.7,
        "max_tokens": 2048
    }
    
    print(f"Frontend request data: {json.dumps(frontend_request, indent=2)}")
    
    try:
        response = requests.post(
            f"{API_BASE}/inference/chat/completions",
            json=frontend_request,
            headers=headers
        )
        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text[:500]}...")  # Truncate for readability
        
        if response.status_code == 200:
            print("✓ Frontend request format works")
            return True
        else:
            print(f"✗ Frontend request format failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                pass
            return False
    except Exception as e:
        print(f"✗ Request failed: {e}")
        return False

def test_non_streaming_request():
    """Test non-streaming request"""
    print("\n2. Testing non-streaming request...")
    
    token = get_auth_token()
    if not token:
        print("✗ Could not get authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    request_data = {
        "model": "Doge-160M",
        "messages": [
            {"role": "user", "content": "Hello, how are you?"}
        ],
        "stream": False,
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    print(f"Request data: {json.dumps(request_data, indent=2)}")
    
    try:
        response = requests.post(
            f"{API_BASE}/inference/chat/completions",
            json=request_data,
            headers=headers
        )
        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 200:
            print("✓ Non-streaming request works")
            return True
        else:
            print(f"✗ Non-streaming request failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                pass
            return False
    except Exception as e:
        print(f"✗ Request failed: {e}")
        return False

def test_validation_errors():
    """Test validation error cases"""
    print("\n3. Testing validation errors...")
    
    token = get_auth_token()
    if not token:
        print("✗ Could not get authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test missing model
    print("\n3a. Testing missing model...")
    missing_model_request = {
        "messages": [
            {"role": "user", "content": "Hello"}
        ]
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/inference/chat/completions",
            json=missing_model_request,
            headers=headers
        )
        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 422:
            print("✓ Missing model correctly rejected")
        else:
            print(f"✗ Missing model should be rejected with 422, got {response.status_code}")
    except Exception as e:
        print(f"✗ Request failed: {e}")
    
    # Test missing messages
    print("\n3b. Testing missing messages...")
    missing_messages_request = {
        "model": "Doge-160M"
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/inference/chat/completions",
            json=missing_messages_request,
            headers=headers
        )
        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 422:
            print("✓ Missing messages correctly rejected")
        else:
            print(f"✗ Missing messages should be rejected with 422, got {response.status_code}")
    except Exception as e:
        print(f"✗ Request failed: {e}")

def main():
    """Run all tests"""
    print("SmallDoge WebUI Chat API Test")
    print("=" * 50)
    
    # Test Pydantic validation first
    if not test_pydantic_validation():
        print("Pydantic validation failed, skipping API tests")
        return False
    
    # Test API validation
    success = True
    success &= test_chat_completion_validation()
    success &= test_non_streaming_request()
    test_validation_errors()  # This doesn't affect success
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Chat API tests passed!")
    else:
        print("❌ Some chat API tests failed.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
