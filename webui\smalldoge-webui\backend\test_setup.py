#!/usr/bin/env python3
"""
Test script to verify SmallDoge WebUI setup
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test if all modules can be imported"""
    print("Testing imports...")
    
    try:
        from smalldoge_webui.env import VERSION, DATA_DIR
        print(f"✓ Environment module imported successfully")
        print(f"  Version: {VERSION}")
        print(f"  Data Directory: {DATA_DIR}")
    except Exception as e:
        print(f"✗ Failed to import environment module: {e}")
        return False
    
    try:
        from smalldoge_webui.constants import ERROR_MESSAGES
        print(f"✓ Constants module imported successfully")
    except Exception as e:
        print(f"✗ Failed to import constants module: {e}")
        return False
    
    try:
        from smalldoge_webui.config import get_app_config
        print(f"✓ Config module imported successfully")
    except Exception as e:
        print(f"✗ Failed to import config module: {e}")
        return False
    
    try:
        from smalldoge_webui.internal.db import Base, get_db
        print(f"✓ Database module imported successfully")
    except Exception as e:
        print(f"✗ Failed to import database module: {e}")
        return False
    
    try:
        from smalldoge_webui.models.users import User, Users
        print(f"✓ User models imported successfully")
    except Exception as e:
        print(f"✗ Failed to import user models: {e}")
        return False
    
    try:
        from smalldoge_webui.utils.auth import get_password_hash
        print(f"✓ Auth utilities imported successfully")
    except Exception as e:
        print(f"✗ Failed to import auth utilities: {e}")
        return False
    
    try:
        from smalldoge_webui.utils.inference import ModelInferenceEngine
        print(f"✓ Inference engine imported successfully")
    except Exception as e:
        print(f"✗ Failed to import inference engine: {e}")
        return False
    
    return True

def test_database():
    """Test database initialization"""
    print("\nTesting database...")
    
    try:
        from smalldoge_webui.internal.db import init_db, check_db_connection
        
        # Initialize database
        init_db()
        print("✓ Database initialized successfully")
        
        # Check connection
        if check_db_connection():
            print("✓ Database connection verified")
        else:
            print("✗ Database connection failed")
            return False
            
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False
    
    return True

def test_config():
    """Test configuration"""
    print("\nTesting configuration...")
    
    try:
        from smalldoge_webui.config import get_app_config
        
        config = get_app_config()
        print(f"✓ Configuration loaded successfully")
        print(f"  WebUI Name: {config.webui_name}")
        print(f"  Version: {config.version}")
        print(f"  Environment: {config.environment}")
        print(f"  Debug: {config.debug}")
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False
    
    return True

def test_auth():
    """Test authentication utilities"""
    print("\nTesting authentication...")
    
    try:
        from smalldoge_webui.utils.auth import get_password_hash, verify_password
        
        # Test password hashing
        password = "test_password_123"
        hashed = get_password_hash(password)
        print("✓ Password hashing works")
        
        # Test password verification
        if verify_password(password, hashed):
            print("✓ Password verification works")
        else:
            print("✗ Password verification failed")
            return False
            
    except Exception as e:
        print(f"✗ Authentication test failed: {e}")
        return False
    
    return True

def test_inference_engine():
    """Test inference engine"""
    print("\nTesting inference engine...")
    
    try:
        from smalldoge_webui.utils.inference import ModelInferenceEngine
        
        engine = ModelInferenceEngine()
        print("✓ Inference engine created successfully")
        print(f"  Device: {engine.device}")
        
        # Test model info
        models = engine.get_loaded_models()
        print(f"✓ Loaded models: {len(models)}")
        
    except Exception as e:
        print(f"✗ Inference engine test failed: {e}")
        return False
    
    return True

def test_fastapi_app():
    """Test FastAPI application"""
    print("\nTesting FastAPI application...")
    
    try:
        from smalldoge_webui.main import app
        print("✓ FastAPI application imported successfully")
        print(f"  Title: {app.title}")
        print(f"  Version: {app.version}")
        
    except Exception as e:
        print(f"✗ FastAPI application test failed: {e}")
        return False
    
    return True

def main():
    """Run all tests"""
    print("SmallDoge WebUI Setup Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_database,
        test_auth,
        test_inference_engine,
        test_fastapi_app,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! SmallDoge WebUI setup is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
