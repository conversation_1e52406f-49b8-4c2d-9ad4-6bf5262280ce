#!/usr/bin/env python3
"""
Test script for SmallDoge WebUI inference engine
"""

import asyncio
import sys
import os
import logging

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smalldoge_webui.utils.inference import ModelInferenceEngine
from smalldoge_webui.constants import MODEL_TYPES

# Setup logging
logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

async def test_inference_engine():
    """Test the inference engine functionality"""
    
    print("🐕 Testing SmallDoge WebUI Inference Engine")
    print("=" * 50)
    
    # Initialize inference engine
    print("1. Initializing inference engine...")
    try:
        engine = ModelInferenceEngine()
        print(f"   ✅ Engine initialized with device: {engine.device}")
    except Exception as e:
        print(f"   ❌ Failed to initialize engine: {e}")
        return False
    
    # Test device detection
    print("\n2. Testing device detection...")
    try:
        print(f"   Device: {engine.device}")
        if hasattr(engine, 'device_map') and engine.device_map:
            print(f"   Device map: {engine.device_map}")
        if hasattr(engine, 'max_memory') and engine.max_memory:
            print(f"   Memory limits: {engine.max_memory}")
        print("   ✅ Device detection working")
    except Exception as e:
        print(f"   ❌ Device detection failed: {e}")
    
    # Test model status (should be empty initially)
    print("\n3. Testing model status...")
    try:
        status = await engine.get_model_status("test-model")
        print(f"   Status for non-existent model: {status}")
        print("   ✅ Model status check working")
    except Exception as e:
        print(f"   ❌ Model status check failed: {e}")
    
    # Test model loading with a simple config
    print("\n4. Testing model loading (simulation)...")
    try:
        test_config = {
            "model_type": MODEL_TYPES.TRANSFORMERS,
            "model_path": "microsoft/DialoGPT-small",  # Small model for testing
            "params": {
                "trust_remote_code": False,
                "torch_dtype": "float32"  # Use float32 for CPU compatibility
            }
        }
        
        print(f"   Attempting to load test model: {test_config['model_path']}")
        success = await engine.load_model("test-model", test_config)
        
        if success:
            print("   ✅ Model loading successful")
            
            # Test model status after loading
            status = await engine.get_model_status("test-model")
            print(f"   Model status after loading: {status}")
            
            # Test unloading
            print("   Testing model unloading...")
            unload_success = await engine.unload_model("test-model")
            if unload_success:
                print("   ✅ Model unloading successful")
            else:
                print("   ❌ Model unloading failed")
                
        else:
            print("   ⚠️  Model loading failed (this is expected if transformers/torch not installed)")
            
    except Exception as e:
        print(f"   ⚠️  Model loading test failed: {e}")
        print("   (This is expected if transformers/torch dependencies are not installed)")
    
    # Test list available models
    print("\n5. Testing list available models...")
    try:
        models = await engine.list_available_models()
        print(f"   Available models: {len(models)} found")
        for model in models[:3]:  # Show first 3 models
            print(f"   - {model.get('id', 'unknown')}: {model.get('name', 'unknown')}")
        print("   ✅ List models working")
    except Exception as e:
        print(f"   ❌ List models failed: {e}")
    
    # Test cleanup
    print("\n6. Testing cleanup...")
    try:
        await engine.cleanup()
        print("   ✅ Cleanup successful")
    except Exception as e:
        print(f"   ❌ Cleanup failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Inference engine test completed!")
    return True

async def test_chat_completion():
    """Test chat completion functionality"""
    
    print("\n🗨️  Testing Chat Completion")
    print("=" * 50)
    
    engine = ModelInferenceEngine()
    
    # Test with a mock model (since we might not have real models loaded)
    print("1. Testing chat completion with mock model...")
    try:
        # Create a mock loaded model for testing
        engine.loaded_models["mock-model"] = {
            "type": MODEL_TYPES.TRANSFORMERS,
            "config": {},
            "loaded_at": 1234567890,
            "status": "loaded"
        }
        
        messages = [
            {"role": "user", "content": "Hello, how are you?"}
        ]
        
        # Test non-streaming
        print("   Testing non-streaming response...")
        try:
            response = await engine.chat_completion(
                model_id="mock-model",
                messages=messages,
                stream=False
            )
            print(f"   Response: {response}")
            print("   ✅ Non-streaming chat completion working")
        except Exception as e:
            print(f"   ⚠️  Non-streaming test failed: {e}")
        
        # Test streaming
        print("   Testing streaming response...")
        try:
            chunks = []
            async for chunk in engine.stream_chat_completion(
                model_id="mock-model",
                messages=messages
            ):
                chunks.append(chunk)
                if len(chunks) >= 3:  # Limit for testing
                    break
            
            print(f"   Received {len(chunks)} chunks")
            print("   ✅ Streaming chat completion working")
        except Exception as e:
            print(f"   ⚠️  Streaming test failed: {e}")
            
    except Exception as e:
        print(f"   ❌ Chat completion test failed: {e}")
    
    await engine.cleanup()

def main():
    """Main test function"""
    print("🚀 Starting SmallDoge WebUI Backend Tests")
    print("This will test the inference engine functionality")
    print()
    
    try:
        # Run inference engine tests
        asyncio.run(test_inference_engine())
        
        # Run chat completion tests
        asyncio.run(test_chat_completion())
        
        print("\n✅ All tests completed!")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Start the backend: python -m smalldoge_webui.main")
        print("3. Start the frontend: python frontend/app.py")
        
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
