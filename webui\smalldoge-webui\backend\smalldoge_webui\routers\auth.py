"""
Authentication Router for SmallDoge WebUI
Based on open-webui auth router structure
"""

import logging
from typing import Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel

from smalldoge_webui.models.auths import (
    Auths, SigninForm, SignupForm, TokenResponse, 
    PasswordChangeForm, AuthModel
)
from smalldoge_webui.models.users import Users, UserModel, UserResponse
from smalldoge_webui.utils.auth import (
    create_access_token, get_current_user, get_admin_user,
    verify_password, get_password_hash
)
from smalldoge_webui.config import AUTH_CONFIG
from smalldoge_webui.constants import ERROR_MESSAGES
from smalldoge_webui.env import SRC_LOG_LEVELS

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["WEBUI"])

router = APIRouter()
security = HTTPBearer(auto_error=False)

class UserSigninResponse(BaseModel):
    """User signin response"""
    token: str
    user: UserResponse

@router.post("/signin", response_model=UserSigninResponse)
async def signin(request: Request, form_data: SigninForm):
    """User signin endpoint"""
    try:
        # Authenticate user
        user = Auths.authenticate_user(form_data.email, form_data.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=ERROR_MESSAGES.INVALID_CREDENTIALS
            )
        
        # Create access token
        token_data = {"sub": user.email, "user_id": user.id}
        access_token = create_access_token(data=token_data)
        
        # Update last active
        Users.update_user_last_active_by_id(user.id)
        
        log.info(f"User {user.email} signed in successfully")
        
        return UserSigninResponse(
            token=access_token,
            user=UserResponse.model_validate(user)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Signin error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.post("/signup", response_model=UserSigninResponse)
async def signup(request: Request, form_data: SignupForm):
    """User signup endpoint"""
    try:
        # Check if signup is enabled
        from smalldoge_webui.config import ENABLE_SIGNUP
        if not ENABLE_SIGNUP.value:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=ERROR_MESSAGES.ACCESS_PROHIBITED
            )
        
        # Check if user already exists
        existing_user = Users.get_user_by_email(form_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=ERROR_MESSAGES.EMAIL_TAKEN
            )
        
        # Create user with auth
        user = Auths.create_user_with_auth(form_data)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create user"
            )
        
        # Create access token
        token_data = {"sub": user.email, "user_id": user.id}
        access_token = create_access_token(data=token_data)
        
        log.info(f"User {user.email} signed up successfully")
        
        return UserSigninResponse(
            token=access_token,
            user=UserResponse.model_validate(user)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Signup error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.post("/signout")
async def signout(response: Response, current_user: UserModel = Depends(get_current_user)):
    """User signout endpoint"""
    try:
        log.info(f"User {current_user.email} signed out")
        
        # In a real implementation, you might want to blacklist the token
        # For now, we just return success
        return {"message": "Signed out successfully"}
        
    except Exception as e:
        log.error(f"Signout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: UserModel = Depends(get_current_user)):
    """Get current user information"""
    try:
        return UserResponse.model_validate(current_user)
    except Exception as e:
        log.error(f"Get current user error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.post("/password/change")
async def change_password(
    form_data: PasswordChangeForm,
    current_user: UserModel = Depends(get_current_user)
):
    """Change user password"""
    try:
        # Get auth record
        auth = Auths.get_auth_by_id(current_user.id)
        if not auth:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.USER_NOT_FOUND
            )
        
        # Verify current password
        from smalldoge_webui.internal.db import get_db
        from smalldoge_webui.models.auths import Auth
        
        with get_db() as db:
            auth_record = db.query(Auth).filter(Auth.id == current_user.id).first()
            if not auth_record or not verify_password(form_data.current_password, auth_record.password):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=ERROR_MESSAGES.INVALID_PASSWORD
                )
        
        # Update password
        new_hashed_password = get_password_hash(form_data.new_password)
        success = Auths.update_password_by_id(current_user.id, new_hashed_password)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ERROR_MESSAGES.DEFAULT("Failed to update password")
            )
        
        log.info(f"Password changed for user {current_user.email}")
        return {"message": "Password changed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Change password error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.post("/token/refresh")
async def refresh_token(current_user: UserModel = Depends(get_current_user)):
    """Refresh access token"""
    try:
        # Create new access token
        token_data = {"sub": current_user.email, "user_id": current_user.id}
        access_token = create_access_token(data=token_data)
        
        return TokenResponse(access_token=access_token)
        
    except Exception as e:
        log.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.get("/users/count")
async def get_users_count():
    """Get total number of users (public endpoint for onboarding check)"""
    try:
        count = Users.get_num_users()
        return {"count": count}
    except Exception as e:
        log.error(f"Get users count error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

# Admin-only endpoints

@router.get("/admin/users")
async def get_all_users(
    skip: int = 0,
    limit: int = 100,
    current_user: UserModel = Depends(get_admin_user)
):
    """Get all users (admin only)"""
    try:
        users = Users.get_users(skip=skip, limit=limit)
        total = Users.get_num_users()
        
        return {
            "users": [UserResponse.model_validate(user) for user in users],
            "total": total,
            "skip": skip,
            "limit": limit
        }
    except Exception as e:
        log.error(f"Get all users error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )

@router.delete("/admin/users/{user_id}")
async def delete_user(
    user_id: str,
    current_user: UserModel = Depends(get_admin_user)
):
    """Delete user (admin only)"""
    try:
        # Don't allow admin to delete themselves
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete your own account"
            )
        
        # Delete auth record first
        auth_deleted = Auths.delete_auth_by_id(user_id)
        
        # Delete user record
        user_deleted = Users.delete_user_by_id(user_id)
        
        if not user_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.USER_NOT_FOUND
            )
        
        log.info(f"User {user_id} deleted by admin {current_user.email}")
        return {"message": "User deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Delete user error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ERROR_MESSAGES.DEFAULT(str(e))
        )
