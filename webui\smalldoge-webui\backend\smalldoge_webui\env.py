"""
Environment Configuration for SmallDoge WebUI
Based on open-webui environment setup
"""

import os
import logging
from pathlib import Path
from typing import Optional

# Base directories
BACKEND_DIR = Path(__file__).parent.parent
BASE_DIR = BACKEND_DIR.parent
DATA_DIR = Path(os.getenv("DATA_DIR", BACKEND_DIR / "data")).resolve()

# Ensure data directory exists
DATA_DIR.mkdir(parents=True, exist_ok=True)

# Environment
ENV = os.environ.get("ENV", "dev")
DEBUG = os.environ.get("DEBUG", "true" if ENV == "dev" else "false").lower() == "true"

# Logging configuration
GLOBAL_LOG_LEVEL = os.environ.get("GLOBAL_LOG_LEVEL", "INFO").upper()

# Source-specific log levels
log_sources = [
    "AUDIO", "COMFYUI", "CONFIG", "DB", "IMAGES", "<PERSON>IN", "MODELS", 
    "OLLAMA", "OP<PERSON><PERSON><PERSON>", "RAG", "WEBHOOK", "WEB<PERSON>", "INFERENCE"
]

SRC_LOG_LEVELS = {}
for source in log_sources:
    log_env_var = source + "_LOG_LEVEL"
    SRC_LOG_LEVELS[source] = os.environ.get(log_env_var, "").upper()
    if SRC_LOG_LEVELS[source] not in logging.getLevelNamesMapping():
        SRC_LOG_LEVELS[source] = GLOBAL_LOG_LEVEL

# Setup basic logging
logging.basicConfig(
    level=getattr(logging, GLOBAL_LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["CONFIG"])

# WebUI Configuration
WEBUI_NAME = os.environ.get("WEBUI_NAME", "SmallDoge WebUI")
WEBUI_FAVICON_URL = os.environ.get("WEBUI_FAVICON_URL", "/static/favicon.png")

# Version and Build Info
VERSION = "1.0.0-dev"
BUILD_HASH = os.environ.get("BUILD_HASH", "dev-build")

# Security
WEBUI_SECRET_KEY = os.environ.get("WEBUI_SECRET_KEY", "")
if not WEBUI_SECRET_KEY:
    import secrets
    WEBUI_SECRET_KEY = secrets.token_urlsafe(32)
    log.warning("WEBUI_SECRET_KEY not set, using generated key. Set this in production!")

# Authentication
WEBUI_AUTH = os.environ.get("WEBUI_AUTH", "true").lower() == "true"
ENABLE_SIGNUP = os.environ.get("ENABLE_SIGNUP", "true").lower() == "true"
ENABLE_LOGIN_FORM = os.environ.get("ENABLE_LOGIN_FORM", "true").lower() == "true"
DEFAULT_USER_ROLE = os.environ.get("DEFAULT_USER_ROLE", "user")

# JWT Configuration
JWT_EXPIRES_IN = os.environ.get("JWT_EXPIRES_IN", "24h")

# Database Configuration
DATABASE_URL = os.environ.get("DATABASE_URL", f"sqlite:///{DATA_DIR}/smalldoge.db")

# Replace postgres:// with postgresql:// for SQLAlchemy compatibility
if "postgres://" in DATABASE_URL:
    DATABASE_URL = DATABASE_URL.replace("postgres://", "postgresql://")

DATABASE_SCHEMA = os.environ.get("DATABASE_SCHEMA", None)

# Database Pool Settings
DATABASE_POOL_SIZE = int(os.environ.get("DATABASE_POOL_SIZE", "10"))
DATABASE_POOL_MAX_OVERFLOW = int(os.environ.get("DATABASE_POOL_MAX_OVERFLOW", "20"))
DATABASE_POOL_TIMEOUT = int(os.environ.get("DATABASE_POOL_TIMEOUT", "30"))
DATABASE_POOL_RECYCLE = int(os.environ.get("DATABASE_POOL_RECYCLE", "3600"))

# Redis Configuration
REDIS_URL = os.environ.get("REDIS_URL", "")
REDIS_SENTINEL_HOSTS = os.environ.get("REDIS_SENTINEL_HOSTS", "")
REDIS_SENTINEL_PORT = os.environ.get("REDIS_SENTINEL_PORT", "26379")

# Model Configuration
MODEL_CACHE_DIR = Path(os.environ.get("MODEL_CACHE_DIR", DATA_DIR / "models"))
MODEL_CACHE_DIR.mkdir(parents=True, exist_ok=True)

DEFAULT_MODEL = os.environ.get("DEFAULT_MODEL", "")
MAX_CONCURRENT_REQUESTS = int(os.environ.get("MAX_CONCURRENT_REQUESTS", "10"))

# Transformers Configuration
TRANSFORMERS_CACHE_DIR = os.environ.get("TRANSFORMERS_CACHE_DIR", str(MODEL_CACHE_DIR / "transformers"))
HUGGINGFACE_HUB_CACHE = os.environ.get("HUGGINGFACE_HUB_CACHE", str(MODEL_CACHE_DIR / "hub"))

# Set transformers cache directories
os.environ["TRANSFORMERS_CACHE"] = TRANSFORMERS_CACHE_DIR
os.environ["HF_HOME"] = HUGGINGFACE_HUB_CACHE

# Device Configuration
DEVICE = os.environ.get("DEVICE", "auto")  # auto, cpu, cuda, mps
TORCH_DTYPE = os.environ.get("TORCH_DTYPE", "auto")  # auto, float16, float32, bfloat16

# Model Loading Configuration
LOAD_IN_8BIT = os.environ.get("LOAD_IN_8BIT", "false").lower() == "true"
LOAD_IN_4BIT = os.environ.get("LOAD_IN_4BIT", "false").lower() == "true"
USE_FLASH_ATTENTION = os.environ.get("USE_FLASH_ATTENTION", "false").lower() == "true"

# External API Configuration
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY", "")
OPENAI_API_BASE_URL = os.environ.get("OPENAI_API_BASE_URL", "https://api.openai.com/v1")
HUGGINGFACE_API_TOKEN = os.environ.get("HUGGINGFACE_API_TOKEN", "")

# CORS Configuration
CORS_ALLOW_ORIGIN = os.environ.get("CORS_ALLOW_ORIGIN", "*")
if CORS_ALLOW_ORIGIN:
    CORS_ALLOW_ORIGIN = [origin.strip() for origin in CORS_ALLOW_ORIGIN.split(",")]
else:
    CORS_ALLOW_ORIGIN = ["*"]

# File Upload Configuration
UPLOAD_DIR = Path(os.environ.get("UPLOAD_DIR", DATA_DIR / "uploads"))
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

MAX_FILE_SIZE = int(os.environ.get("MAX_FILE_SIZE", "10485760"))  # 10MB

# Rate Limiting
RATE_LIMIT_ENABLED = os.environ.get("RATE_LIMIT_ENABLED", "true").lower() == "true"
RATE_LIMIT_PER_MINUTE = int(os.environ.get("RATE_LIMIT_PER_MINUTE", "60"))

# WebSocket Configuration
ENABLE_WEBSOCKET_SUPPORT = os.environ.get("ENABLE_WEBSOCKET_SUPPORT", "true").lower() == "true"

# Monitoring and Health Checks
ENABLE_HEALTH_CHECKS = os.environ.get("ENABLE_HEALTH_CHECKS", "true").lower() == "true"
ENABLE_METRICS = os.environ.get("ENABLE_METRICS", "true").lower() == "true"

# Development Configuration
ENABLE_API_DOCS = os.environ.get("ENABLE_API_DOCS", "true" if ENV == "dev" else "false").lower() == "true"
RELOAD_ON_CHANGE = os.environ.get("RELOAD_ON_CHANGE", "true" if ENV == "dev" else "false").lower() == "true"

# Server Configuration
HOST = os.environ.get("HOST", "0.0.0.0")
PORT = int(os.environ.get("PORT", "8000"))
UVICORN_WORKERS = int(os.environ.get("UVICORN_WORKERS", "1"))

# Thread Pool Configuration
THREAD_POOL_SIZE = int(os.environ.get("THREAD_POOL_SIZE", "10"))

# Gradio Frontend Configuration
GRADIO_HOST = os.environ.get("GRADIO_HOST", "0.0.0.0")
GRADIO_PORT = int(os.environ.get("GRADIO_PORT", "7860"))
GRADIO_SHARE = os.environ.get("GRADIO_SHARE", "false").lower() == "true"

# Log configuration summary
log.info(f"SmallDoge WebUI Environment Configuration:")
log.info(f"  Environment: {ENV}")
log.info(f"  Debug Mode: {DEBUG}")
log.info(f"  Data Directory: {DATA_DIR}")
log.info(f"  Database URL: {DATABASE_URL}")
log.info(f"  Model Cache Directory: {MODEL_CACHE_DIR}")
log.info(f"  Device: {DEVICE}")
log.info(f"  Max Concurrent Requests: {MAX_CONCURRENT_REQUESTS}")
log.info(f"  CORS Origins: {CORS_ALLOW_ORIGIN}")
log.info(f"  Server: {HOST}:{PORT}")
log.info(f"  Gradio Frontend: {GRADIO_HOST}:{GRADIO_PORT}")

# Validate critical configurations
if WEBUI_AUTH and not WEBUI_SECRET_KEY:
    log.error("WEBUI_SECRET_KEY is required when authentication is enabled!")
    
if not DATABASE_URL:
    log.error("DATABASE_URL is required!")

# Export commonly used paths
__all__ = [
    "ENV", "DEBUG", "DATA_DIR", "MODEL_CACHE_DIR", "UPLOAD_DIR",
    "DATABASE_URL", "WEBUI_SECRET_KEY", "VERSION", "BUILD_HASH",
    "SRC_LOG_LEVELS", "log"
]
