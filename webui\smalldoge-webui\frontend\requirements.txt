# SmallDoge WebUI Frontend Dependencies
# Gradio-based frontend for SmallDoge WebUI

# Core Gradio framework
gradio

# HTTP client for API communication
requests

# JSON handling (built-in, but listed for clarity)
# json

# Environment variable handling
python-dotenv

# Optional: Enhanced UI components
# gradio-client==0.7.0

# Optional: Additional utilities
# pandas==2.1.3  # For data handling
# numpy==1.24.3  # For numerical operations

# Development dependencies
# pytest==7.4.3  # For testing
# black==23.11.0  # For code formatting
