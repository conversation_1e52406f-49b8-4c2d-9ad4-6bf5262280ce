#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add a working test model to the database
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def add_test_model():
    """Add a working test model to the database"""
    print("Adding test model to database...")
    
    try:
        from smalldoge_webui.models.models import Models, ModelCreate
        from smalldoge_webui.internal.db import init_db
        
        # Initialize database
        init_db()
        print("✓ Database initialized")
        
        # Check if test model already exists
        existing_model = Models.get_model_by_id("gpt2-small")
        if existing_model:
            print("✓ Test model 'gpt2-small' already exists")
            return True
        
        # Create test model
        test_model_data = ModelCreate(
            id="gpt2-small",
            name="GPT-2 Small",
            description="GPT-2 Small - A reliable test model",
            model_type="transformers",
            model_path="gpt2",  # This is a known working model
            is_public=True
        )
        
        # Add to database with a test user ID
        test_user_id = "system"
        created_model = Models.create_model(test_model_data, test_user_id)
        
        if created_model:
            print("✓ Test model 'gpt2-small' added successfully")
            print(f"  ID: {created_model.id}")
            print(f"  Name: {created_model.name}")
            print(f"  Path: {created_model.model_path}")
            return True
        else:
            print("✗ Failed to create test model")
            return False
            
    except Exception as e:
        print(f"✗ Error adding test model: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_distilgpt2_model():
    """Add DistilGPT-2 model as another option"""
    print("\nAdding DistilGPT-2 model...")
    
    try:
        from smalldoge_webui.models.models import Models, ModelCreate
        
        # Check if model already exists
        existing_model = Models.get_model_by_id("distilgpt2")
        if existing_model:
            print("✓ DistilGPT-2 model already exists")
            return True
        
        # Create DistilGPT-2 model
        distilgpt2_model_data = ModelCreate(
            id="distilgpt2",
            name="DistilGPT-2",
            description="DistilGPT-2 - Smaller and faster GPT-2",
            model_type="transformers",
            model_path="distilgpt2",
            is_public=True
        )
        
        # Add to database
        test_user_id = "system"
        created_model = Models.create_model(distilgpt2_model_data, test_user_id)
        
        if created_model:
            print("✓ DistilGPT-2 model added successfully")
            print(f"  ID: {created_model.id}")
            print(f"  Name: {created_model.name}")
            print(f"  Path: {created_model.model_path}")
            return True
        else:
            print("✗ Failed to create DistilGPT-2 model")
            return False
            
    except Exception as e:
        print(f"✗ Error adding DistilGPT-2 model: {e}")
        return False

def list_available_models():
    """List all available models in the database"""
    print("\nListing available models...")
    
    try:
        from smalldoge_webui.models.models import Models
        
        models = Models.get_models()
        
        if models:
            print(f"Found {len(models)} models:")
            for model in models:
                print(f"  - {model.id}: {model.name} ({model.model_path})")
        else:
            print("No models found in database")
            
        return True
        
    except Exception as e:
        print(f"✗ Error listing models: {e}")
        return False

def test_model_access():
    """Test if the added models can be accessed"""
    print("\nTesting model access...")
    
    test_models = ["gpt2", "distilgpt2"]
    
    for model_name in test_models:
        print(f"\nTesting {model_name}...")
        try:
            from transformers import AutoTokenizer, AutoConfig
            
            # Test tokenizer
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            print(f"✓ {model_name} tokenizer accessible")
            
            # Test config
            config = AutoConfig.from_pretrained(model_name)
            print(f"✓ {model_name} config accessible")
            print(f"  Model type: {config.model_type}")
            
        except Exception as e:
            print(f"✗ {model_name} not accessible: {e}")

def main():
    """Main function"""
    print("SmallDoge WebUI - Add Test Models")
    print("=" * 50)
    
    # Test model access first
    test_model_access()
    
    # Add test models
    success1 = add_test_model()
    success2 = add_distilgpt2_model()
    
    # List all models
    list_available_models()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 Test models added successfully!")
        print("\nYou can now use these models in the frontend:")
        print("  - gpt2-small (GPT-2 Small)")
        print("  - distilgpt2 (DistilGPT-2)")
        print("\nThese models should work reliably for testing.")
    else:
        print("❌ Some models failed to add.")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
