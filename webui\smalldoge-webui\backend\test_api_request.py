#!/usr/bin/env python3
"""
Test script to simulate frontend API requests
"""

import sys
import os
import requests
import json
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Backend API configuration
BACKEND_URL = "http://localhost:8000"
API_BASE = f"{BACKEND_URL}/api/v1"

def test_health_check():
    """Test backend health check"""
    print("Testing backend health check...")
    
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✓ Backend is running")
            data = response.json()
            print(f"  Status: {data.get('status')}")
            print(f"  Version: {data.get('version')}")
            return True
        else:
            print(f"✗ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Backend connection failed: {e}")
        return False

def test_user_signup():
    """Test user signup"""
    print("\nTesting user signup...")
    
    try:
        # Test signup
        signup_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = requests.post(f"{API_BASE}/auth/signup", json=signup_data)
        print(f"Signup response status: {response.status_code}")
        print(f"Signup response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("token")
            if token:
                print("✓ User signup successful")
                return token
            else:
                print("✗ No token in signup response")
                return None
        else:
            print(f"✗ User signup failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"✗ User signup error: {e}")
        return None

def test_user_signin():
    """Test user signin"""
    print("\nTesting user signin...")
    
    try:
        # Test signin
        signin_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = requests.post(f"{API_BASE}/auth/signin", json=signin_data)
        print(f"Signin response status: {response.status_code}")
        print(f"Signin response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("token")
            if token:
                print("✓ User signin successful")
                return token
            else:
                print("✗ No token in signin response")
                return None
        else:
            print(f"✗ User signin failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"✗ User signin error: {e}")
        return None

def test_model_creation(token):
    """Test model creation"""
    print("\nTesting model creation...")
    
    if not token:
        print("✗ No authentication token available")
        return False
    
    try:
        # Test model creation with exact data from frontend
        model_data = {
            "id": "test-doge-160m",
            "name": "Test Doge-160M",
            "description": "Test Doge-160M - transformers model",
            "model_type": "transformers",
            "model_path": "SmallDoge/Doge-160M",
            "config": {
                "max_length": 2048,
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 50,
                "repetition_penalty": 1.1,
                "do_sample": True,
                "load_in_8bit": False,
                "load_in_4bit": False,
                "device_map": "auto",
                "torch_dtype": "auto",
                "trust_remote_code": False
            },
            "is_public": True
        }
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        print(f"Sending model data: {json.dumps(model_data, indent=2)}")
        
        response = requests.post(f"{API_BASE}/models", json=model_data, headers=headers)
        print(f"Model creation response status: {response.status_code}")
        print(f"Model creation response: {response.text}")
        
        if response.status_code in [200, 201]:
            print("✓ Model creation successful")
            return True
        else:
            print(f"✗ Model creation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                pass
            return False
            
    except Exception as e:
        print(f"✗ Model creation error: {e}")
        return False

def test_model_list(token):
    """Test model list"""
    print("\nTesting model list...")
    
    if not token:
        print("✗ No authentication token available")
        return False
    
    try:
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        response = requests.get(f"{API_BASE}/models", headers=headers)
        print(f"Model list response status: {response.status_code}")
        print(f"Model list response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            models = data.get("models", [])
            print(f"✓ Model list successful - found {len(models)} models")
            for model in models:
                print(f"  - {model.get('id')}: {model.get('name')}")
            return True
        else:
            print(f"✗ Model list failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ Model list error: {e}")
        return False

def main():
    """Run all tests"""
    print("SmallDoge WebUI API Test")
    print("=" * 50)
    
    # Test backend health
    if not test_health_check():
        print("Backend is not running. Please start the backend first.")
        return False
    
    # Try to get authentication token
    token = test_user_signin()
    if not token:
        token = test_user_signup()
    
    if not token:
        print("Could not authenticate. Skipping authenticated tests.")
        return False
    
    # Test authenticated endpoints
    success = True
    success &= test_model_creation(token)
    success &= test_model_list(token)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
