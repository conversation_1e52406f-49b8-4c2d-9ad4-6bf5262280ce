@echo off
REM Simple startup script for SmallDoge WebUI

echo Starting SmallDoge WebUI...
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    pause
    exit /b 1
)

echo Python found
echo.

REM Setup backend
echo Setting up backend...
cd backend
if not exist "venv" (
    echo Creating backend venv...
    python -m venv venv
)
call venv\Scripts\activate.bat
pip install -r requirements.txt >nul 2>&1
echo Backend ready
cd ..

REM Setup frontend  
echo Setting up frontend...
cd frontend
if not exist "venv" (
    echo Creating frontend venv...
    python -m venv venv
)
call venv\Scripts\activate.bat
pip install -r requirements.txt >nul 2>&1
echo Frontend ready
cd ..

REM Start backend
echo Starting backend...
start "Backend" cmd /k "cd backend && venv\Scripts\activate.bat && python -m smalldoge_webui.main"

REM Wait and start frontend
echo Waiting 10 seconds...
timeout /t 10 /nobreak >nul

echo Starting frontend...
start "Frontend" cmd /k "cd frontend && venv\Scripts\activate.bat && python app.py"

echo.
echo Services started!
echo Backend: http://localhost:8000
echo Frontend: http://localhost:7860
echo.
echo Press any key to open browser...
pause >nul
start http://localhost:7860
