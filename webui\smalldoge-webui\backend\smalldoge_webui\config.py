"""
Configuration Management for SmallDoge WebUI
Based on open-webui configuration system with persistent config support
"""

import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Generic, Optional, TypeVar, Union
from pydantic import BaseModel

from smalldoge_webui.env import (
    DATA_DIR, ENV, DATABASE_URL, WEBUI_AUTH, WEBUI_NAME, 
    SRC_LOG_LEVELS, WEBUI_SECRET_KEY
)

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["CONFIG"])

T = TypeVar('T')

class PersistentConfig(Generic[T]):
    """
    Persistent configuration class that stores values in JSON file
    Similar to open-webui's PersistentConfig but simplified
    """
    
    def __init__(self, env_name: str, config_path: str, default_value: T):
        self.env_name = env_name
        self.config_path = config_path
        self.default_value = default_value
        self._config_file = DATA_DIR / "config.json"
        self._config_data = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            if self._config_file.exists():
                with open(self._config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            log.warning(f"Failed to load config file: {e}")
        return {}
    
    def _save_config(self):
        """Save configuration to JSON file"""
        try:
            self._config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self._config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            log.error(f"Failed to save config file: {e}")
    
    def _get_nested_value(self, data: Dict[str, Any], path: str) -> Any:
        """Get nested value from dictionary using dot notation"""
        keys = path.split('.')
        current = data
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        return current
    
    def _set_nested_value(self, data: Dict[str, Any], path: str, value: Any):
        """Set nested value in dictionary using dot notation"""
        keys = path.split('.')
        current = data
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[keys[-1]] = value
    
    @property
    def value(self) -> T:
        """Get current configuration value"""
        # First check environment variable
        env_value = os.environ.get(self.env_name)
        if env_value is not None:
            # Try to parse JSON for complex types
            try:
                return json.loads(env_value)
            except (json.JSONDecodeError, TypeError):
                # Return as string if not valid JSON
                return env_value
        
        # Then check persistent config
        config_value = self._get_nested_value(self._config_data, self.config_path)
        if config_value is not None:
            return config_value
            
        # Return default value
        return self.default_value
    
    @value.setter
    def value(self, new_value: T):
        """Set configuration value"""
        self._set_nested_value(self._config_data, self.config_path, new_value)
        self._save_config()
    
    def __call__(self) -> T:
        """Allow calling the config object to get value"""
        return self.value

# Database Configuration
DATABASE_CONFIG = {
    "url": DATABASE_URL,
    "echo": ENV == "dev",
    "pool_size": int(os.environ.get("DATABASE_POOL_SIZE", "10")),
    "max_overflow": int(os.environ.get("DATABASE_POOL_MAX_OVERFLOW", "20")),
    "pool_timeout": int(os.environ.get("DATABASE_POOL_TIMEOUT", "30")),
    "pool_recycle": int(os.environ.get("DATABASE_POOL_RECYCLE", "3600")),
}

# Authentication Configuration
AUTH_CONFIG = {
    "secret_key": WEBUI_SECRET_KEY,
    "algorithm": "HS256",
    "access_token_expire_minutes": int(os.environ.get("ACCESS_TOKEN_EXPIRE_MINUTES", "1440")),  # 24 hours
    "refresh_token_expire_days": int(os.environ.get("REFRESH_TOKEN_EXPIRE_DAYS", "7")),
}

# WebUI Configuration
WEBUI_URL = PersistentConfig("WEBUI_URL", "webui.url", os.environ.get("WEBUI_URL", ""))

ENABLE_SIGNUP = PersistentConfig(
    "ENABLE_SIGNUP",
    "auth.enable_signup",
    (
        False if not WEBUI_AUTH 
        else os.environ.get("ENABLE_SIGNUP", "True").lower() == "true"
    ),
)

ENABLE_LOGIN_FORM = PersistentConfig(
    "ENABLE_LOGIN_FORM",
    "auth.enable_login_form",
    os.environ.get("ENABLE_LOGIN_FORM", "True").lower() == "true",
)

DEFAULT_USER_ROLE = PersistentConfig(
    "DEFAULT_USER_ROLE",
    "auth.default_user_role",
    os.environ.get("DEFAULT_USER_ROLE", "user"),
)

# Model Configuration
DEFAULT_MODEL = PersistentConfig(
    "DEFAULT_MODEL",
    "models.default",
    os.environ.get("DEFAULT_MODEL", ""),
)

MODEL_CACHE_DIR = PersistentConfig(
    "MODEL_CACHE_DIR",
    "models.cache_dir",
    os.environ.get("MODEL_CACHE_DIR", str(DATA_DIR / "models")),
)

MAX_CONCURRENT_REQUESTS = PersistentConfig(
    "MAX_CONCURRENT_REQUESTS",
    "models.max_concurrent_requests",
    int(os.environ.get("MAX_CONCURRENT_REQUESTS", "10")),
)

# Device Configuration
DEVICE = PersistentConfig(
    "DEVICE",
    "models.device",
    os.environ.get("DEVICE", "auto"),
)

TORCH_DTYPE = PersistentConfig(
    "TORCH_DTYPE", 
    "models.torch_dtype",
    os.environ.get("TORCH_DTYPE", "auto"),
)

LOAD_IN_8BIT = PersistentConfig(
    "LOAD_IN_8BIT",
    "models.load_in_8bit",
    os.environ.get("LOAD_IN_8BIT", "false").lower() == "true",
)

LOAD_IN_4BIT = PersistentConfig(
    "LOAD_IN_4BIT",
    "models.load_in_4bit", 
    os.environ.get("LOAD_IN_4BIT", "false").lower() == "true",
)

# External API Configuration
OPENAI_API_KEY = PersistentConfig(
    "OPENAI_API_KEY",
    "external.openai.api_key",
    os.environ.get("OPENAI_API_KEY", ""),
)

OPENAI_API_BASE_URL = PersistentConfig(
    "OPENAI_API_BASE_URL",
    "external.openai.base_url",
    os.environ.get("OPENAI_API_BASE_URL", "https://api.openai.com/v1"),
)

HUGGINGFACE_API_TOKEN = PersistentConfig(
    "HUGGINGFACE_API_TOKEN",
    "external.huggingface.api_token",
    os.environ.get("HUGGINGFACE_API_TOKEN", ""),
)

# UI Configuration
UI_CONFIG = {
    "theme": PersistentConfig("UI_THEME", "ui.theme", "light"),
    "language": PersistentConfig("UI_LANGUAGE", "ui.language", "en"),
    "show_admin_details": PersistentConfig(
        "SHOW_ADMIN_DETAILS", 
        "ui.show_admin_details",
        os.environ.get("SHOW_ADMIN_DETAILS", "true").lower() == "true"
    ),
}

# Rate Limiting Configuration
RATE_LIMIT_CONFIG = {
    "enabled": PersistentConfig(
        "RATE_LIMIT_ENABLED",
        "rate_limit.enabled",
        os.environ.get("RATE_LIMIT_ENABLED", "true").lower() == "true"
    ),
    "per_minute": PersistentConfig(
        "RATE_LIMIT_PER_MINUTE",
        "rate_limit.per_minute",
        int(os.environ.get("RATE_LIMIT_PER_MINUTE", "60"))
    ),
}

# File Upload Configuration
UPLOAD_CONFIG = {
    "max_file_size": PersistentConfig(
        "MAX_FILE_SIZE",
        "upload.max_file_size",
        int(os.environ.get("MAX_FILE_SIZE", "10485760"))  # 10MB
    ),
    "allowed_extensions": PersistentConfig(
        "ALLOWED_EXTENSIONS",
        "upload.allowed_extensions",
        [".txt", ".md", ".json", ".csv", ".pdf", ".doc", ".docx"]
    ),
}

# Application Configuration Class
class AppConfig(BaseModel):
    """Application configuration model"""
    webui_name: str = WEBUI_NAME
    version: str = "1.0.0-dev"
    environment: str = ENV
    debug: bool = ENV == "dev"
    enable_signup: bool = ENABLE_SIGNUP.value
    enable_login_form: bool = ENABLE_LOGIN_FORM.value
    default_user_role: str = DEFAULT_USER_ROLE.value
    default_model: str = DEFAULT_MODEL.value
    max_concurrent_requests: int = MAX_CONCURRENT_REQUESTS.value
    
    class Config:
        extra = "allow"

def get_app_config() -> AppConfig:
    """Get current application configuration"""
    return AppConfig()

def reset_config():
    """Reset configuration to defaults"""
    config_file = DATA_DIR / "config.json"
    if config_file.exists():
        try:
            config_file.unlink()
            log.info("Configuration reset to defaults")
        except Exception as e:
            log.error(f"Failed to reset configuration: {e}")

# Export commonly used configurations
__all__ = [
    "PersistentConfig", "AppConfig", "get_app_config", "reset_config",
    "DATABASE_CONFIG", "AUTH_CONFIG", "WEBUI_URL", "ENABLE_SIGNUP",
    "ENABLE_LOGIN_FORM", "DEFAULT_USER_ROLE", "DEFAULT_MODEL",
    "MODEL_CACHE_DIR", "MAX_CONCURRENT_REQUESTS", "DEVICE", "TORCH_DTYPE",
    "LOAD_IN_8BIT", "LOAD_IN_4BIT", "OPENAI_API_KEY", "OPENAI_API_BASE_URL",
    "HUGGINGFACE_API_TOKEN", "UI_CONFIG", "RATE_LIMIT_CONFIG", "UPLOAD_CONFIG"
]
