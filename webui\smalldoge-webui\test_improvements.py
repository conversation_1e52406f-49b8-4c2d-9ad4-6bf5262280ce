#!/usr/bin/env python3
"""
Test script for SmallDoge WebUI improvements
"""

import sys
import os
import requests
import json
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Backend API configuration
BACKEND_URL = "http://localhost:8000"
API_BASE = f"{BACKEND_URL}/api/v1"

def test_health_check():
    """Test enhanced health check"""
    print("Testing enhanced health check...")
    
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        print(f"Health check status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Health check response:")
            print(f"  Status: {data.get('status')}")
            print(f"  Version: {data.get('version')}")
            print(f"  Database: {data.get('database')}")
            print(f"  Environment: {data.get('environment')}")
            return True
        else:
            print(f"✗ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ Health check error: {e}")
        return False

def get_auth_token():
    """Get authentication token"""
    try:
        # Try to sign in with existing user
        signin_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }

        response = requests.post(f"{API_BASE}/auth/signin", json=signin_data)
        if response.status_code == 200:
            data = response.json()
            return data.get("token")

        # If signin fails, try to create the user
        print(f"Signin failed: {response.status_code}, trying to create user...")

        signup_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }

        response = requests.post(f"{API_BASE}/auth/signup", json=signup_data)
        if response.status_code == 200:
            data = response.json()
            return data.get("token")

        print(f"Signup also failed: {response.status_code} - {response.text}")
        return None

    except Exception as e:
        print(f"Error getting auth token: {e}")
        return None

def test_model_status():
    """Test model status endpoint"""
    print("\nTesting model status endpoint...")
    
    token = get_auth_token()
    if not token:
        print("✗ Could not get authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test with Doge-160M model
    model_id = "Doge-160M"
    
    try:
        response = requests.get(f"{API_BASE}/models/{model_id}/status", headers=headers)
        print(f"Model status response: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Model status for {model_id}:")
            print(f"  Loaded: {data.get('loaded')}")
            print(f"  Status: {data.get('status')}")
            print(f"  Device: {data.get('device')}")
            if data.get('memory_usage'):
                memory = data['memory_usage']
                print(f"  Memory allocated: {memory.get('allocated', 0):.1f}GB")
                print(f"  Memory cached: {memory.get('cached', 0):.1f}GB")
            return True
        else:
            print(f"✗ Model status failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Model status error: {e}")
        return False

def test_model_loading():
    """Test model loading endpoint"""
    print("\nTesting model loading endpoint...")
    
    token = get_auth_token()
    if not token:
        print("✗ Could not get authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test with Doge-160M model
    model_id = "Doge-160M"
    
    try:
        response = requests.post(f"{API_BASE}/models/{model_id}/load", headers=headers)
        print(f"Model load response: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Model loading result:")
            print(f"  Message: {data.get('message')}")
            print(f"  Loaded: {data.get('loaded')}")
            return True
        else:
            print(f"✗ Model loading failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Model loading error: {e}")
        return False

def test_enhanced_model_list():
    """Test enhanced model list with status"""
    print("\nTesting enhanced model list...")
    
    token = get_auth_token()
    if not token:
        print("✗ Could not get authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{API_BASE}/models", headers=headers)
        print(f"Model list response: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            models = data.get("models", [])
            print(f"✓ Found {len(models)} models:")
            for model in models:
                print(f"  - {model.get('id')}: {model.get('name')}")
                print(f"    Type: {model.get('model_type')}")
                print(f"    Path: {model.get('model_path')}")
                print(f"    Active: {model.get('is_active')}")
            return True
        else:
            print(f"✗ Model list failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Model list error: {e}")
        return False

def main():
    """Run all improvement tests"""
    print("SmallDoge WebUI Improvements Test")
    print("=" * 50)
    
    # Test 1: Enhanced health check
    health_success = test_health_check()
    
    # Test 2: Model status
    status_success = test_model_status()
    
    # Test 3: Model loading
    loading_success = test_model_loading()
    
    # Test 4: Enhanced model list
    list_success = test_enhanced_model_list()
    
    print("\n" + "=" * 50)
    print("IMPROVEMENT TEST RESULTS:")
    print("=" * 50)
    print(f"Health check: {'✓' if health_success else '✗'}")
    print(f"Model status: {'✓' if status_success else '✗'}")
    print(f"Model loading: {'✓' if loading_success else '✗'}")
    print(f"Enhanced model list: {'✓' if list_success else '✗'}")
    
    if all([health_success, status_success, loading_success, list_success]):
        print("\n🎉 ALL IMPROVEMENTS WORKING!")
        print("The enhanced features are ready to use.")
    else:
        print("\n⚠️ Some improvements need attention.")
        print("Check the error messages above for details.")
    
    return all([health_success, status_success, loading_success, list_success])

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
