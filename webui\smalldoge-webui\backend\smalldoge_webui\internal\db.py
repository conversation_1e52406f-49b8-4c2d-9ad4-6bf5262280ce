"""
Database Configuration and Connection Management
Based on open-webui database setup with SQLAlchemy
"""

import logging
from contextlib import contextmanager
from typing import Any, Optional

from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy.pool import Que<PERSON><PERSON><PERSON>, NullPool
from sqlalchemy.exc import SQLAlchemyError

from smalldoge_webui.config import DATABASE_CONFIG
from smalldoge_webui.env import SRC_LOG_LEVELS, DATABASE_URL, DATABASE_SCHEMA

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["DB"])

# Database URL
SQLALCHEMY_DATABASE_URL = DATABASE_URL

# Create engine based on database type
if "sqlite" in SQLALCHEMY_DATABASE_URL:
    # SQLite configuration
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        connect_args={"check_same_thread": False},
        echo=DATABASE_CONFIG["echo"]
    )
    log.info("Using SQLite database")
else:
    # PostgreSQL/MySQL configuration with connection pooling
    if DATABASE_CONFIG["pool_size"] > 0:
        engine = create_engine(
            SQLALCHEMY_DATABASE_URL,
            pool_size=DATABASE_CONFIG["pool_size"],
            max_overflow=DATABASE_CONFIG["max_overflow"],
            pool_timeout=DATABASE_CONFIG["pool_timeout"],
            pool_recycle=DATABASE_CONFIG["pool_recycle"],
            pool_pre_ping=True,
            poolclass=QueuePool,
            echo=DATABASE_CONFIG["echo"]
        )
        log.info(f"Using connection pool with size {DATABASE_CONFIG['pool_size']}")
    else:
        engine = create_engine(
            SQLALCHEMY_DATABASE_URL,
            pool_pre_ping=True,
            poolclass=NullPool,
            echo=DATABASE_CONFIG["echo"]
        )
        log.info("Using database without connection pooling")

# Session configuration
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False
)

# Metadata with optional schema
metadata_obj = MetaData(schema=DATABASE_SCHEMA)

# Base class for all models
Base = declarative_base(metadata=metadata_obj)

# Scoped session for thread safety
Session = scoped_session(SessionLocal)

def get_session():
    """
    Get database session generator
    Use this with dependency injection in FastAPI
    """
    db = SessionLocal()
    try:
        yield db
    except SQLAlchemyError as e:
        log.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

@contextmanager
def get_db():
    """
    Get database session context manager
    Use this for direct database operations
    """
    db = SessionLocal()
    try:
        yield db
    except SQLAlchemyError as e:
        log.error(f"Database operation error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def init_db():
    """
    Initialize database tables
    Creates all tables defined in models
    """
    try:
        # Import all models to ensure they are registered
        from smalldoge_webui.models import users, models, chats, auths
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        log.info("Database tables created successfully")
        
        # Verify database connection
        with get_db() as db:
            db.execute(text("SELECT 1"))
            log.info("Database connection verified")
            
    except Exception as e:
        log.error(f"Failed to initialize database: {e}")
        raise

def check_db_connection() -> bool:
    """
    Check if database connection is working
    Returns True if connection is successful, False otherwise
    """
    try:
        with get_db() as db:
            db.execute(text("SELECT 1"))
            return True
    except Exception as e:
        log.error(f"Database connection check failed: {e}")
        return False

def get_db_info() -> dict:
    """
    Get database information
    Returns dictionary with database details
    """
    try:
        with get_db() as db:
            # Get database version and other info
            if "sqlite" in SQLALCHEMY_DATABASE_URL:
                result = db.execute(text("SELECT sqlite_version()")).fetchone()
                db_version = result[0] if result else "unknown"
                db_type = "SQLite"
            elif "postgresql" in SQLALCHEMY_DATABASE_URL:
                result = db.execute(text("SELECT version()")).fetchone()
                db_version = result[0].split()[1] if result else "unknown"
                db_type = "PostgreSQL"
            elif "mysql" in SQLALCHEMY_DATABASE_URL:
                result = db.execute(text("SELECT VERSION()")).fetchone()
                db_version = result[0] if result else "unknown"
                db_type = "MySQL"
            else:
                db_version = "unknown"
                db_type = "Unknown"
            
            return {
                "type": db_type,
                "version": db_version,
                "url": SQLALCHEMY_DATABASE_URL.split("@")[-1] if "@" in SQLALCHEMY_DATABASE_URL else SQLALCHEMY_DATABASE_URL,
                "schema": DATABASE_SCHEMA,
                "pool_size": DATABASE_CONFIG["pool_size"],
                "connected": True
            }
    except Exception as e:
        log.error(f"Failed to get database info: {e}")
        return {
            "type": "Unknown",
            "version": "unknown",
            "url": "unknown",
            "schema": DATABASE_SCHEMA,
            "pool_size": DATABASE_CONFIG["pool_size"],
            "connected": False,
            "error": str(e)
        }

def reset_db():
    """
    Reset database by dropping and recreating all tables
    WARNING: This will delete all data!
    """
    try:
        log.warning("Resetting database - all data will be lost!")
        
        # Drop all tables
        Base.metadata.drop_all(bind=engine)
        log.info("All tables dropped")
        
        # Recreate all tables
        Base.metadata.create_all(bind=engine)
        log.info("All tables recreated")
        
        return True
    except Exception as e:
        log.error(f"Failed to reset database: {e}")
        return False

def backup_db(backup_path: str) -> bool:
    """
    Backup database (SQLite only)
    For other databases, use database-specific backup tools
    """
    if "sqlite" not in SQLALCHEMY_DATABASE_URL:
        log.error("Database backup only supported for SQLite")
        return False
    
    try:
        import shutil
        from pathlib import Path
        
        # Extract database file path from URL
        db_file = SQLALCHEMY_DATABASE_URL.replace("sqlite:///", "")
        backup_file = Path(backup_path)
        
        # Ensure backup directory exists
        backup_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Copy database file
        shutil.copy2(db_file, backup_file)
        log.info(f"Database backed up to {backup_file}")
        return True
        
    except Exception as e:
        log.error(f"Failed to backup database: {e}")
        return False

# Database health check for monitoring
def health_check() -> dict:
    """
    Perform database health check
    Returns status information for monitoring
    """
    try:
        start_time = time.time()
        
        with get_db() as db:
            # Simple query to test connection
            db.execute(text("SELECT 1"))
            
        response_time = time.time() - start_time
        
        return {
            "status": "healthy",
            "response_time": response_time,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

# Import required modules for health check
import time
from datetime import datetime

# Log database configuration
log.info(f"Database configuration:")
log.info(f"  URL: {SQLALCHEMY_DATABASE_URL.split('@')[-1] if '@' in SQLALCHEMY_DATABASE_URL else SQLALCHEMY_DATABASE_URL}")
log.info(f"  Schema: {DATABASE_SCHEMA}")
log.info(f"  Pool Size: {DATABASE_CONFIG['pool_size']}")
log.info(f"  Echo: {DATABASE_CONFIG['echo']}")

# Export commonly used objects
__all__ = [
    "engine", "SessionLocal", "Base", "Session",
    "get_session", "get_db", "init_db", "check_db_connection",
    "get_db_info", "reset_db", "backup_db", "health_check"
]
