"""
SmallDoge WebUI Constants
Based on open-webui architecture with transformers inference backend
"""

# Error Messages
class ERROR_MESSAGES:
    DEFAULT = lambda err="": f"Something went wrong {'(' + err + ')' if err else ''}."
    UNAUTHORIZED = "Unauthorized"
    ACCESS_PROHIBITED = "Access prohibited"
    NOT_FOUND = "Not found"
    USER_NOT_FOUND = "User not found"
    MODEL_NOT_FOUND = "Model not found"
    INVALID_TOKEN = "Invalid token"
    INVALID_CREDENTIALS = "Invalid credentials"
    EMAIL_TAKEN = "Email is already taken"
    USERNAME_TAKEN = "Username is already taken"
    MODEL_ID_TAKEN = "Model ID is already taken"
    INVALID_PASSWORD = "Invalid password"
    WEAK_PASSWORD = "Password is too weak"
    INVALID_EMAIL = "Invalid email format"
    
    # Model related errors
    MODEL_LOADING_ERROR = "Error loading model"
    MODEL_INFERENCE_ERROR = "Error during model inference"
    UNSUPPORTED_MODEL_TYPE = "Unsupported model type"
    
    # File related errors
    FILE_NOT_FOUND = "File not found"
    FILE_TOO_LARGE = "File too large"
    INVALID_FILE_TYPE = "Invalid file type"
    
    # API related errors
    RATE_LIMIT_EXCEEDED = "Rate limit exceeded"
    INVALID_REQUEST = "Invalid request"
    MISSING_REQUIRED_FIELD = "Missing required field"

# Success Messages
class SUCCESS_MESSAGES:
    USER_CREATED = "User created successfully"
    USER_UPDATED = "User updated successfully"
    USER_DELETED = "User deleted successfully"
    MODEL_CREATED = "Model created successfully"
    MODEL_UPDATED = "Model updated successfully"
    MODEL_DELETED = "Model deleted successfully"
    LOGIN_SUCCESS = "Login successful"
    LOGOUT_SUCCESS = "Logout successful"

# Model Types
class MODEL_TYPES:
    TRANSFORMERS = "transformers"
    HUGGINGFACE = "huggingface"
    LOCAL = "local"
    OPENAI = "openai"
    OLLAMA = "ollama"

# User Roles
class USER_ROLES:
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"

# Chat Message Roles
class MESSAGE_ROLES:
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"

# File Types
class SUPPORTED_FILE_TYPES:
    TEXT = [".txt", ".md", ".json", ".csv"]
    IMAGES = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"]
    DOCUMENTS = [".pdf", ".doc", ".docx"]
    
# Model Configuration Defaults
DEFAULT_MODEL_CONFIG = {
    "max_length": 2048,
    "temperature": 0.7,
    "top_p": 0.9,
    "top_k": 50,
    "repetition_penalty": 1.1,
    "do_sample": True,
    "pad_token_id": None,
    "eos_token_id": None,
}

# API Limits
API_LIMITS = {
    "max_tokens": 4096,
    "max_messages": 100,
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "rate_limit_per_minute": 60,
}

# Database Constants
DB_CONSTANTS = {
    "max_connections": 20,
    "connection_timeout": 30,
    "query_timeout": 60,
}

# Cache Settings
CACHE_SETTINGS = {
    "model_cache_ttl": 3600,  # 1 hour
    "user_cache_ttl": 1800,   # 30 minutes
    "config_cache_ttl": 300,  # 5 minutes
}

# Logging Levels
LOG_LEVELS = {
    "DEBUG": "DEBUG",
    "INFO": "INFO", 
    "WARNING": "WARNING",
    "ERROR": "ERROR",
    "CRITICAL": "CRITICAL",
}

# WebUI Settings
WEBUI_SETTINGS = {
    "default_theme": "light",
    "default_language": "en",
    "session_timeout": 24 * 60 * 60,  # 24 hours
    "max_chat_history": 1000,
}

# Model Download Sources
MODEL_SOURCES = {
    "huggingface": "https://huggingface.co",
    "local": "local",
}

# Supported Model Architectures
SUPPORTED_ARCHITECTURES = [
    "LlamaForCausalLM",
    "GPT2LMHeadModel", 
    "BloomForCausalLM",
    "OPTForCausalLM",
    "T5ForConditionalGeneration",
    "BartForConditionalGeneration",
]

# Default Prompts
DEFAULT_PROMPTS = {
    "system": "You are a helpful AI assistant.",
    "welcome": "Hello! How can I help you today?",
    "error": "I apologize, but I encountered an error. Please try again.",
}

# Version Information
VERSION_INFO = {
    "major": 1,
    "minor": 0,
    "patch": 0,
    "build": "dev",
}

VERSION = f"{VERSION_INFO['major']}.{VERSION_INFO['minor']}.{VERSION_INFO['patch']}-{VERSION_INFO['build']}"
