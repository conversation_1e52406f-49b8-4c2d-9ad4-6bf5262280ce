@echo off
REM SmallDoge WebUI Frontend Startup Script for Windows

echo Starting SmallDoge WebUI Frontend...
echo.

REM Set environment variables
set BACKEND_URL=http://localhost:8000
set GRADIO_SERVER_NAME=0.0.0.0
set GRADIO_SERVER_PORT=7860

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to your PATH
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Error: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

REM Check if backend is running
echo Checking backend connection...
python -c "import requests; requests.get('http://localhost:8000/health', timeout=5)" >nul 2>&1
if errorlevel 1 (
    echo Warning: Backend server is not running or not accessible
    echo Please make sure the backend is running on http://localhost:8000
    echo You can start it with: cd ..\backend && start_windows.bat
    echo.
    echo Press any key to continue anyway, or Ctrl+C to exit...
    pause >nul
)

REM Start the frontend
echo.
echo Starting SmallDoge WebUI Frontend...
echo Frontend will be available at: http://localhost:7860
echo Backend API: %BACKEND_URL%
echo.
echo Press Ctrl+C to stop the frontend
echo.

python app.py

REM Pause on exit to see any error messages
if errorlevel 1 (
    echo.
    echo Frontend stopped with error code %errorlevel%
    pause
)
